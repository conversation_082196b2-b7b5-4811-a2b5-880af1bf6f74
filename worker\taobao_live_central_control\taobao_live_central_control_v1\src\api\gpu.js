/**
 * GPU实例管理API路由
 * 集成象工云GPU服务，实现批量AI音频生成功能
 */

import express from 'express';
import fs from 'fs/promises';
import path from 'path';
import database from '../database.js';
import {
    getAvailableGpuAccount,
    updateGpuAccountLastUsed,
    fetchXiangongyunInstances,
    findAvailableInstance,
    updateInstanceName,
    getAudioDirectoryPath,
    processScriptContent
} from '../utils/gpu-utils.js';

const router = express.Router();



/**
 * 获取可用GPU实例
 * POST /api/gpu/instances
 */
router.post('/instances', async (req, res) => {
    try {
        const { liveId, anchorName } = req.body;

        if (!liveId) {
            return res.status(400).json({
                success: false,
                error: '直播ID不能为空'
            });
        }

        // 获取主播信息
        let finalAnchorName = anchorName;
        if (!finalAnchorName) {
            const anchorInfo = await getAnchorInfoByLiveId(liveId);
            if (!anchorInfo) {
                return res.status(404).json({
                    success: false,
                    error: '未找到对应的主播信息'
                });
            }
            finalAnchorName = anchorInfo.anchor_name;
        }

        // 获取可用的GPU账户
        const gpuAccount = await getAvailableGpuAccount();
        if (!gpuAccount) {
            return res.status(404).json({
                success: false,
                error: '暂无可用的GPU账户，请先在GPU账户管理中添加账户'
            });
        }

        console.log(`使用GPU账户: ${gpuAccount.account}, Token: ${gpuAccount.token ? '已配置' : '未配置'}`);

        // 调用象工云API获取实例列表
        const instances = await fetchXiangongyunInstances(gpuAccount.token);

        // 筛选可用实例
        const availableInstance = findAvailableInstance(instances);
        
        if (!availableInstance) {
            return res.status(404).json({
                success: false,
                error: '暂无可用GPU实例，请稍后重试或检查实例状态'
            });
        }

        // 更新实例名称
        await updateInstanceName(availableInstance.id, `${finalAnchorName}-使用中`, gpuAccount.token);
        
        // 添加主播信息到实例对象
        availableInstance.anchor_name = finalAnchorName;
        availableInstance.live_id = liveId;
        availableInstance.gpu_account_id = gpuAccount.id;

        // 更新GPU账户最后使用时间
        await updateGpuAccountLastUsed(gpuAccount.id);

        res.json({
            success: true,
            instance: availableInstance
        });

    } catch (error) {
        console.error('获取GPU实例失败:', error);
        res.status(500).json({
            success: false,
            error: '获取GPU实例失败: ' + error.message
        });
    }
});

/**
 * 检查音频文件目录（内部函数）
 */
async function checkAudioDirectory(anchorName) {
    const directoryPath = await getAudioDirectoryPath(anchorName);

    try {
        // 检查目录是否存在
        await fs.access(directoryPath);

        // 读取目录中的文件
        const files = await fs.readdir(directoryPath);

        // 筛选音频文件
        const audioFiles = files.filter(file => {
            const ext = path.extname(file).toLowerCase();
            return ['.wav', '.mp3', '.m4a', '.aac', '.flac'].includes(ext);
        });

        return {
            success: true,
            hasFiles: audioFiles.length > 0,
            fileCount: audioFiles.length,
            directoryPath: directoryPath
        };

    } catch (error) {
        // 目录不存在
        return {
            success: true,
            hasFiles: false,
            fileCount: 0,
            directoryPath: directoryPath
        };
    }
}

/**
 * 检查音频文件目录
 * POST /api/gpu/check-directory
 */
router.post('/check-directory', async (req, res) => {
    try {
        const { anchorName } = req.body;

        if (!anchorName) {
            return res.status(400).json({
                success: false,
                error: '主播名称不能为空'
            });
        }

        const result = await checkAudioDirectory(anchorName);
        res.json(result);

    } catch (error) {
        console.error('检查目录失败:', error);
        res.status(500).json({
            success: false,
            error: '检查目录失败: ' + error.message
        });
    }
});

/**
 * 清理音频文件目录（内部函数）
 */
async function cleanupAudioDirectory(anchorName) {
    const directoryPath = await getAudioDirectoryPath(anchorName);

    try {
        // 检查目录是否存在
        await fs.access(directoryPath);

        // 读取目录中的文件
        const files = await fs.readdir(directoryPath);

        let deletedCount = 0;
        for (const file of files) {
            // 跳过.verysync文件
            if (file === '.verysync') {
                continue;
            }

            const filePath = path.join(directoryPath, file);
            const stat = await fs.stat(filePath);

            if (stat.isFile()) {
                await fs.unlink(filePath);
                deletedCount++;
            }
        }

        return {
            success: true,
            message: `成功删除 ${deletedCount} 个文件`,
            deletedCount: deletedCount
        };

    } catch (error) {
        if (error.code === 'ENOENT') {
            return {
                success: true,
                message: '目录不存在，无需清理',
                deletedCount: 0
            };
        } else {
            throw error;
        }
    }
}

/**
 * 清理音频文件目录
 * POST /api/gpu/cleanup-directory
 */
router.post('/cleanup-directory', async (req, res) => {
    try {
        const { anchorName } = req.body;

        if (!anchorName) {
            return res.status(400).json({
                success: false,
                error: '主播名称不能为空'
            });
        }

        const result = await cleanupAudioDirectory(anchorName);
        res.json(result);

    } catch (error) {
        console.error('清理目录失败:', error);
        res.status(500).json({
            success: false,
            error: '清理目录失败: ' + error.message
        });
    }
});

/**
 * 创建批量音频生成任务
 * POST /api/gpu/audio-generation/create
 */
router.post('/audio-generation/create', async (req, res) => {
    try {
        const {
            liveId,
            startSequence,
            endSequence,
            speed,
            randomSpeed,
            autoDestroy,
            concurrency,
            instanceId,
            publicId,
            anchorName,
            autoCreateGpu
        } = req.body;

        // 验证必填参数
        if (!liveId || !startSequence || !endSequence || !anchorName) {
            return res.status(400).json({
                success: false,
                error: '缺少必要的参数'
            });
        }

        // 如果不是自动创建GPU模式，则需要验证实例参数
        if (!autoCreateGpu && (!instanceId || !publicId)) {
            return res.status(400).json({
                success: false,
                error: '手动模式下需要提供GPU实例参数'
            });
        }

        if (startSequence > endSequence) {
            return res.status(400).json({
                success: false,
                error: '开始序号不能大于结束序号'
            });
        }

        // 查询产品数据
        const products = await getProductsBySequenceRange(liveId, startSequence, endSequence);
        
        if (products.length === 0) {
            return res.status(404).json({
                success: false,
                error: '未找到指定序号范围内的产品'
            });
        }

        // 创建任务记录
        const taskParams = {
            speed: speed || 1.2,
            randomSpeed: randomSpeed || false,
            autoDestroy: autoDestroy || false,
            concurrency: concurrency || 20,
            autoCreateGpu: autoCreateGpu || false,
            instanceId: instanceId,
            publicId: publicId,
            anchorName: anchorName,
            productCount: products.length
        };

        const taskId = await createAudioGenerationTask(liveId, 'audio', taskParams);

        // 为每个产品创建子任务
        for (const product of products) {
            await createProductAudioTask(taskId, product, taskParams);
        }

        // 触发任务管理器立即处理新任务
        const { default: taskManager } = await import('../task-manager.js');
        taskManager.processNextTask();

        res.json({
            success: true,
            taskId: taskId,
            message: `成功创建批量音频生成任务，共 ${products.length} 个产品，任务将立即开始执行`
        });

    } catch (error) {
        console.error('创建音频生成任务失败:', error);
        res.status(500).json({
            success: false,
            error: '创建任务失败: ' + error.message
        });
    }
});

// 辅助函数

/**
 * 根据直播ID获取主播信息
 */
async function getAnchorInfoByLiveId(liveId) {
    try {
        const result = await database.get(
            'SELECT DISTINCT anchor_name FROM live_plans WHERE live_id = ? LIMIT 1',
            [liveId]
        );
        return result;
    } catch (error) {
        console.error('获取主播信息失败:', error);
        return null;
    }
}



/**
 * 根据序号范围查询产品
 */
async function getProductsBySequenceRange(liveId, startSequence, endSequence) {
    try {
        const result = await database.all(`
            SELECT product_id, product_name, product_sequence, script_content, anchor_name
            FROM live_products
            WHERE live_id = ?
                AND product_sequence >= ?
                AND product_sequence <= ?
                AND script_content IS NOT NULL
                AND script_content != ''
            ORDER BY product_sequence DESC
        `, [liveId, startSequence, endSequence]);

        return result.results || [];
    } catch (error) {
        console.error('查询产品失败:', error);
        throw error;
    }
}

/**
 * 创建音频生成任务
 */
async function createAudioGenerationTask(liveId, taskType, taskParams) {
    try {
        // 先在主任务表中创建任务记录
        const result = await database.run(`
            INSERT INTO async_tasks (
                task_name, task_type, anchor_name, live_id, task_params, 
                task_count, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, 'pending', datetime('now', 'localtime'))
        `, [
            `批量音频生成-${taskParams.anchorName}`, 
            taskType, 
            taskParams.anchorName, 
            liveId, 
            JSON.stringify(taskParams),
            taskParams.productCount
        ]);

        if (!result.success) {
            throw new Error('创建任务记录失败');
        }

        return result.meta.last_row_id;
    } catch (error) {
        console.error('创建任务记录失败:', error);
        throw error;
    }
}

/**
 * 为产品创建音频生成子任务
 */
async function createProductAudioTask(taskId, product, taskParams) {
    try {
        // 处理手卡内容
        const processedScript = processScriptContent(product.script_content);
        
        // 构建音频生成参数
        const audioParams = {
            text: processedScript,
            speaker: `${taskParams.anchorName}.pt`,
            speed: taskParams.speed,
            filename: `${product.product_id}.wav`,
            productId: product.product_id,
            sequence: product.product_sequence
        };

        // 在task_details表中创建子任务记录
        await database.run(`
            INSERT INTO task_details (
                task_id, product_id, product_name, sequence_number, status,
                result_data, created_at
            ) VALUES (?, ?, ?, ?, 'pending', ?, datetime('now', 'localtime'))
        `, [taskId, product.product_id, product.product_name, product.product_sequence, JSON.stringify(audioParams)]);

        // 更新产品状态
        await database.run(`
            UPDATE live_products 
            SET audio_extraction_status = 'pending', updated_at = datetime('now', 'localtime')
            WHERE product_id = ?
        `, [product.product_id]);

    } catch (error) {
        console.error('创建产品音频任务失败:', error);
        throw error;
    }
}

// 导出需要的函数供其他模块使用
export { getProductsBySequenceRange, createAudioGenerationTask, createProductAudioTask, checkAudioDirectory, cleanupAudioDirectory };

export default router;