import { database } from '../database.js';

/**
 * 主播相关工具函数
 */

/**
 * 根据主播名称获取主播信息
 * @param {string} anchorName - 主播名称
 * @returns {Promise<object|null>} - 主播信息或null
 */
export async function getAnchorByName(anchorName) {
  try {
    const result = await database.get(
      "SELECT id, anchor_name, anchor_cookie FROM anchors WHERE anchor_name = ?",
      [anchorName]
    );
    return result || null;
  } catch (error) {
    console.error('获取主播信息失败:', error);
    return null;
  }
}

/**
 * 验证主播存在性并返回错误响应
 * @param {object} anchor - 主播信息
 * @param {object} res - Express响应对象
 * @returns {boolean} - 是否存在
 */
export function validateAnchorExists(anchor, res) {
  if (!anchor) {
    res.status(404).json({
      error: "Anchor not found",
      message: "未找到指定的主播"
    });
    return false;
  }
  return true;
}

/**
 * 验证主播是否配置了Cookie
 * @param {object} anchor - 主播信息
 * @param {object} res - Express响应对象
 * @returns {boolean} - 是否配置了Cookie
 */
export function validateAnchorCookie(anchor, res) {
  if (!anchor.anchor_cookie) {
    res.status(400).json({
      error: "Missing token",
      message: "该主播未配置Token，请先在主播管理中配置"
    });
    return false;
  }
  return true;
}

/**
 * 获取所有活跃的主播（用于批量操作）
 * @returns {Promise<Array>} - 活跃主播列表
 */
export async function getActiveAnchors() {
  try {
    const result = await database.all(
      "SELECT id, anchor_name, anchor_cookie FROM anchors WHERE status = 'active' AND anchor_cookie IS NOT NULL AND anchor_cookie != ''"
    );
    return result.results || [];
  } catch (error) {
    console.error('获取活跃主播失败:', error);
    return [];
  }
}

/**
 * 根据直播ID获取主播信息
 * @param {string} liveId - 直播ID
 * @returns {Promise<object|null>} - 主播信息或null
 */
export async function getAnchorByLiveId(liveId) {
  try {
    const livePlanResult = await database.get(
      "SELECT anchor_name FROM live_plans WHERE live_id = ?",
      [liveId]
    );
    
    if (!livePlanResult) {
      return null;
    }
    
    return await getAnchorByName(livePlanResult.anchor_name);
  } catch (error) {
    console.error('根据直播ID获取主播信息失败:', error);
    return null;
  }
}
