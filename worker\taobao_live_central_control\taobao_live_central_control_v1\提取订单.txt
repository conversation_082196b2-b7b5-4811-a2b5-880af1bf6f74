创建直播计划按钮后 添加提取订单按钮，以下是请求内容：
fetch("https://h5api.m.taobao.com/h5/mtop.dreamweb.query.general.generalquery/1.0/?jsv=2.7.4&appKey=12574478&t=1754451087421&sign=77654909cc523eade384168533ba2d37&api=mtop.dreamweb.query.general.generalQuery&v=1.0&dataType=jsonp&preventFallback=true&type=jsonp&callback=mtopjsonp245&data=%7B%22dataApi%22%3A%22dataQRForm%22%2C%22param%22%3A%22%7B%5C%22dataQRFormId%5C%22%3A%5C%22live_overview_order%5C%22%2C%5C%22queryUserRole%5C%22%3A%5C%22ALL%5C%22%2C%5C%22beginTime%5C%22%3A%5C%222025-07-28%2000%3A00%3A00%5C%22%2C%5C%22endTime%5C%22%3A%5C%222025-08-05%2000%3A00%3A59%5C%22%2C%5C%22orderDateType%5C%22%3A%5C%223%5C%22%2C%5C%22start%5C%22%3A%5C%220%5C%22%2C%5C%22hit%5C%22%3A%5C%2210%5C%22%7D%22%7D", {
  "headers": {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "script",
    "sec-fetch-mode": "no-cors",
    "sec-fetch-site": "same-site"
  },
  "referrer": "https://liveplatform.taobao.com/restful/index/data/transaction",
  "referrerPolicy": "no-referrer-when-downgrade",
  "body": null,
  "method": "GET",
  "mode": "cors",
  "credentials": "include"
});

以下是响应内容：
mtopjsonp245({
    "api": "mtop.dreamweb.query.general.generalquery",
    "data": {
        "result": [
            {
                "divPayAmt": "258.00",
                "confirmPaidAmt": "-",
                "mordId": "2851555911194231086",
                "cateLevel1Name": "保健用品",
                "daiboName": "小新甄选",
                "refundAmt": "-",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "-",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "朗佑堂沐童舒 小儿皮肤浴养弱酸药浴乳 中药浴养宝宝沐浴露",
                "isSelf": "否",
                "liveStartTime": "2025-07-28 07:34:58",
                "cateLevel1Id": "********",
                "createTime": "2025-07-28 08:45:37",
                "contentId": "************",
                "itemId": "************",
                "index": "1",
                "fansType": "-",
                "payTime": "2025-07-28 08:45:43",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "2851555911194231086"
            },
            {
                "divPayAmt": "158.00",
                "confirmPaidAmt": "-",
                "mordId": "2856601562322174960",
                "cateLevel1Name": "医疗器械",
                "daiboName": "小新甄选",
                "refundAmt": "-",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "-",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "诺斯清高渗缓冲生理性海盐水洗鼻器儿童鼻腔护理鼻炎鼻塞喷雾剂",
                "isSelf": "否",
                "liveStartTime": "2025-07-31 15:37:36",
                "cateLevel1Id": "********",
                "createTime": "2025-07-31 16:54:57",
                "contentId": "************",
                "itemId": "************",
                "index": "2",
                "fansType": "-",
                "payTime": "2025-07-31 16:55:02",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "2856601562322174960"
            },
            {
                "divPayAmt": "156.00",
                "confirmPaidAmt": "-",
                "mordId": "2857595486542304393",
                "cateLevel1Name": "医疗器械",
                "daiboName": "小新甄选",
                "refundAmt": "-",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "-",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "创福康医用修复敷料水乳剂术后补水创面激素依赖性皮炎敏感肌",
                "isSelf": "否",
                "liveStartTime": "2025-08-01 07:11:07",
                "cateLevel1Id": "********",
                "createTime": "2025-08-01 12:09:23",
                "contentId": "************",
                "itemId": "************",
                "index": "3",
                "fansType": "-",
                "payTime": "2025-08-01 12:09:24",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "2857595486542304393"
            },
            {
                "divPayAmt": "0.00",
                "confirmPaidAmt": "-",
                "mordId": "2858747666947945969",
                "cateLevel1Name": "其他",
                "daiboName": "小新甄选",
                "refundAmt": "-",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "-",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "【赠品】碧缇福口腔脱敏膏牙膏1支(100g)",
                "isSelf": "否",
                "liveStartTime": "2025-08-02 07:16:36",
                "cateLevel1Id": "********",
                "createTime": "2025-08-02 09:25:59",
                "contentId": "************",
                "itemId": "************",
                "index": "4",
                "fansType": "-",
                "payTime": "2025-08-02 09:26:12",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "2858747666951945969"
            },
            {
                "divPayAmt": "510.00",
                "confirmPaidAmt": "510.00",
                "mordId": "2859821904572964751",
                "cateLevel1Name": "保健食品/膳食营养补充食品",
                "daiboName": "小新甄选",
                "refundAmt": "-",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "-",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "金七鲜三七口服液官方正品旗舰店辅助调节血压高 增强提升免疫力",
                "isSelf": "否",
                "liveStartTime": "2025-08-02 16:46:55",
                "cateLevel1Id": "********",
                "createTime": "2025-08-02 19:47:20",
                "contentId": "************",
                "itemId": "************",
                "index": "5",
                "fansType": "-",
                "payTime": "2025-08-02 19:47:30",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "2025-08-05 11:31:56",
                "orderId": "2859821904572964751"
            },
            {
                "divPayAmt": "198.00",
                "confirmPaidAmt": "-",
                "mordId": "4655338922735004123",
                "cateLevel1Name": "玩具/童车/益智/积木/模型",
                "daiboName": "小新甄选",
                "refundAmt": "-",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "-",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "凯叔中国传统故事故事机4岁+早教机学习儿童礼物启蒙嫦娥",
                "isSelf": "否",
                "liveStartTime": "2025-07-28 07:34:58",
                "cateLevel1Id": "25",
                "createTime": "2025-07-28 09:26:21",
                "contentId": "************",
                "itemId": "************",
                "index": "6",
                "fansType": "-",
                "payTime": "2025-07-28 09:26:23",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "4655338922735004123"
            },
            {
                "divPayAmt": "8.90",
                "confirmPaidAmt": "-",
                "mordId": "4657085570229055233",
                "cateLevel1Name": "保健用品",
                "daiboName": "小新甄选",
                "refundAmt": "-",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "-",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "【U先试用】元气达人关节膝盖腱鞘贴手腕腰肩贴运动护理1片/袋*5",
                "isSelf": "否",
                "liveStartTime": "2025-07-28 07:34:58",
                "cateLevel1Id": "********",
                "createTime": "2025-07-29 11:32:30",
                "contentId": "************",
                "itemId": "************",
                "index": "7",
                "fansType": "-",
                "payTime": "2025-07-29 11:32:33",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "4657085570229055233"
            },
            {
                "divPayAmt": "175.55",
                "confirmPaidAmt": "-",
                "mordId": "4662167760031914825",
                "cateLevel1Name": "保健食品/膳食营养补充食品",
                "daiboName": "小新甄选",
                "refundAmt": "-",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "-",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "【直播专享】诺特兰德液体钙铁锌儿营养包官方旗舰店非童氨基丁酸",
                "isSelf": "否",
                "liveStartTime": "2025-08-01 07:11:07",
                "cateLevel1Id": "********",
                "createTime": "2025-08-01 15:51:19",
                "contentId": "************",
                "itemId": "************",
                "index": "8",
                "fansType": "-",
                "payTime": "2025-08-01 15:51:25",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "4662167760031914825"
            },
            {
                "divPayAmt": "152.10",
                "confirmPaidAmt": "-",
                "mordId": "4663014588089668731",
                "cateLevel1Name": "传统滋补营养品",
                "daiboName": "小新甄选",
                "refundAmt": "-",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "-",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "张宝山铁棍山药粉怀淮山药粉河南焦作山药速食代餐粉255g*4",
                "isSelf": "否",
                "liveStartTime": "2025-08-02 07:16:36",
                "cateLevel1Id": "********",
                "createTime": "2025-08-02 08:44:50",
                "contentId": "************",
                "itemId": "************",
                "index": "9",
                "fansType": "-",
                "payTime": "2025-08-02 08:44:57",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "4663014588089668731"
            },
            {
                "divPayAmt": "368.97",
                "confirmPaidAmt": "-",
                "mordId": "4663993178861414413",
                "cateLevel1Name": "保健食品/膳食营养补充食品",
                "daiboName": "小新甄选",
                "refundAmt": "368.97",
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "2025-08-02 18:36:08",
                "buyerNickMark": "-",
                "accountName": "小新甄选",
                "itemTitle": "宋雨琦SERYBOX v2妖精咖啡韩国藤黄果生酮阻断促燃0糖代谢咖啡液",
                "isSelf": "否",
                "liveStartTime": "2025-08-02 16:46:55",
                "cateLevel1Id": "********",
                "createTime": "2025-08-02 18:34:41",
                "contentId": "************",
                "itemId": "************",
                "index": "10",
                "fansType": "-",
                "payTime": "2025-08-02 18:34:45",
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "4663993178861414413"
            }
        ]
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "traceId": "213e07c617544510874106424e11fa",
    "v": "1.0"
})


需求：
1. 找到同步直播计划功能，查看如何调用接口的，如何参数加密的，如何使用cookie的，参考以下然后调用提取订单接口
2. 修改为json请求
3. 点击提取订单按钮，显示modal框，第一行显示时间段选择框，开始时间默认为00：00：00，结束时间默认为：23:59:59 ，第二列是快捷选择按钮：昨天，3天，7天，15天（因为获取不到今天的订单，默认使用当前日期减去1天作为结束时间，然后再进行计算开始日期，仅限于选择了快捷日期减去一天，自定义日期不用减去1天）
4. 对应到live_order中的字段是：
                "divPayAmt": "368.97",  --支付金额
                "confirmPaidAmt": "-",
                "mordId": "4663993178861414413", --父订单id
                "cateLevel1Name": "保健食品/膳食营养补充食品",
                "daiboName": "小新甄选", --主播名称
                "refundAmt": "368.97", --退款金额
                "contentTitle": "守护健康 安全可靠",
                "caseEndTime": "2025-08-02 18:36:08",
                "buyerNickMark": "-", 
                "accountName": "小新甄选", 
                "itemTitle": "宋雨琦SERYBOX v2妖精咖啡韩国藤黄果生酮阻断促燃0糖代谢咖啡液",  --产品标题
                "isSelf": "否",
                "liveStartTime": "2025-08-02 16:46:55",
                "cateLevel1Id": "********",
                "createTime": "2025-08-02 18:34:41",
                "contentId": "************",  --直播id
                "itemId": "************",  --产品id
                "index": "10",  
                "fansType": "-",
                "payTime": "2025-08-02 18:34:45", --支付时间
                "accountId": "*************",
                "daiboId": "-",
                "confirmPaidTime": "-",
                "orderId": "4663993178861414413" --子订单ID

5.不要写重复代码，要完全看懂已有的接口的逻辑再写代码，不要写烂代码。逻辑单独写到一个js里