import express from 'express';
import database from '../database.js';

const router = express.Router();

/**
 * 获取GPU账号统计信息
 */
router.get('/stats', async (req, res) => {
    try {
        const result = await database.all(`
            SELECT 
                status,
                COUNT(*) as count
            FROM gpu_accounts
            GROUP BY status
        `);
        
        const stats = result.results || [];
        
        // 统计汇总
        const summary = {
            total: 0,
            active: 0,
            inactive: 0,
            expired: 0
        };
        
        stats.forEach(stat => {
            summary.total += stat.count;
            summary[stat.status] = stat.count;
        });

        res.json({
            success: true,
            stats: summary
        });

    } catch (error) {
        console.error('获取GPU账号统计失败:', error);
        res.status(500).json({
            success: false,
            error: '获取统计信息失败',
            message: error.message
        });
    }
});

/**
 * 获取GPU账号列表
 */
router.get('/', async (req, res) => {
    try {
        const { search, status, page = 1, limit = 50 } = req.query;
        
        let sql = `
            SELECT
                id, account, password, token, api_key, image_id, ding_key, status, description,
                last_used_at, created_at, updated_at
            FROM gpu_accounts
        `;
        
        const conditions = [];
        const params = [];

        if (search) {
            conditions.push('(account LIKE ? OR description LIKE ?)');
            params.push(`%${search}%`, `%${search}%`);
        }
        
        if (status) {
            conditions.push('status = ?');
            params.push(status);
        }

        if (conditions.length > 0) {
            sql += ' WHERE ' + conditions.join(' AND ');
        }

        sql += ' ORDER BY created_at DESC';

        const result = await database.all(sql, params);
        const accounts = result.results || [];
        
        // 分页处理
        const offset = (parseInt(page) - 1) * parseInt(limit);
        const paginatedAccounts = accounts.slice(offset, offset + parseInt(limit));

        res.json({
            success: true,
            accounts: paginatedAccounts,
            pagination: {
                total: accounts.length,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(accounts.length / parseInt(limit))
            }
        });

    } catch (error) {
        console.error('获取GPU账号列表失败:', error);
        res.status(500).json({
            success: false,
            error: '获取账号列表失败',
            message: error.message
        });
    }
});

/**
 * 获取单个GPU账号详情
 */
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const account = await database.get(
            'SELECT * FROM gpu_accounts WHERE id = ?',
            [id]
        );
        
        if (!account) {
            return res.status(404).json({
                success: false,
                error: '账号不存在'
            });
        }

        res.json({
            success: true,
            account: account
        });

    } catch (error) {
        console.error('获取GPU账号详情失败:', error);
        res.status(500).json({
            success: false,
            error: '获取账号详情失败',
            message: error.message
        });
    }
});

/**
 * 创建新的GPU账号
 */
router.post('/', async (req, res) => {
    try {
        const { account, password, token, api_key, image_id, ding_key, description } = req.body;

        if (!account || !password) {
            return res.status(400).json({
                success: false,
                error: '账号和密码不能为空'
            });
        }

        // 检查账号是否已存在
        const existingAccount = await database.get(
            'SELECT id FROM gpu_accounts WHERE account = ?',
            [account]
        );

        if (existingAccount) {
            return res.status(400).json({
                success: false,
                error: '账号已存在'
            });
        }

        // 创建新账号
        const result = await database.run(`
            INSERT INTO gpu_accounts (
                account, password, token, api_key, image_id, ding_key, description, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active')
        `, [account, password, token || null, api_key || null, image_id || null, ding_key || null, description || null]);

        if (result.success) {
            res.json({
                success: true,
                accountId: result.meta.last_row_id,
                message: 'GPU账号创建成功'
            });
        } else {
            throw new Error('数据库插入失败');
        }

    } catch (error) {
        console.error('创建GPU账号失败:', error);
        res.status(500).json({
            success: false,
            error: '创建账号失败',
            message: error.message
        });
    }
});

/**
 * 更新GPU账号信息
 */
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { account, password, token, api_key, image_id, ding_key, description } = req.body;

        if (!account || !password) {
            return res.status(400).json({
                success: false,
                error: '账号和密码不能为空'
            });
        }

        // 检查账号是否存在
        const existingAccount = await database.get(
            'SELECT id FROM gpu_accounts WHERE id = ?',
            [id]
        );

        if (!existingAccount) {
            return res.status(404).json({
                success: false,
                error: '账号不存在'
            });
        }

        // 检查账号名是否与其他账号冲突
        const conflictAccount = await database.get(
            'SELECT id FROM gpu_accounts WHERE account = ? AND id != ?',
            [account, id]
        );

        if (conflictAccount) {
            return res.status(400).json({
                success: false,
                error: '账号名已被其他账号使用'
            });
        }

        // 更新账号信息
        const result = await database.run(`
            UPDATE gpu_accounts
            SET account = ?, password = ?, token = ?, api_key = ?, image_id = ?, ding_key = ?, description = ?,
                updated_at = datetime('now', 'localtime')
            WHERE id = ?
        `, [account, password, token || null, api_key || null, image_id || null, ding_key || null, description || null, id]);

        if (result.success) {
            res.json({
                success: true,
                message: 'GPU账号更新成功'
            });
        } else {
            throw new Error('数据库更新失败');
        }

    } catch (error) {
        console.error('更新GPU账号失败:', error);
        res.status(500).json({
            success: false,
            error: '更新账号失败',
            message: error.message
        });
    }
});

/**
 * 更新GPU账号状态
 */
router.put('/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;

        if (!['active', 'inactive', 'expired'].includes(status)) {
            return res.status(400).json({
                success: false,
                error: '无效的状态值'
            });
        }

        // 检查账号是否存在
        const existingAccount = await database.get(
            'SELECT id FROM gpu_accounts WHERE id = ?',
            [id]
        );

        if (!existingAccount) {
            return res.status(404).json({
                success: false,
                error: '账号不存在'
            });
        }

        // 更新账号状态
        const result = await database.run(`
            UPDATE gpu_accounts 
            SET status = ?, updated_at = datetime('now', 'localtime')
            WHERE id = ?
        `, [status, id]);

        if (result.success) {
            res.json({
                success: true,
                message: 'GPU账号状态更新成功'
            });
        } else {
            throw new Error('数据库更新失败');
        }

    } catch (error) {
        console.error('更新GPU账号状态失败:', error);
        res.status(500).json({
            success: false,
            error: '更新账号状态失败',
            message: error.message
        });
    }
});

/**
 * 更新GPU账号最后使用时间
 */
router.put('/:id/last-used', async (req, res) => {
    try {
        const { id } = req.params;

        // 检查账号是否存在
        const existingAccount = await database.get(
            'SELECT id FROM gpu_accounts WHERE id = ?',
            [id]
        );

        if (!existingAccount) {
            return res.status(404).json({
                success: false,
                error: '账号不存在'
            });
        }

        // 更新最后使用时间
        const result = await database.run(`
            UPDATE gpu_accounts 
            SET last_used_at = datetime('now', 'localtime'), 
                updated_at = datetime('now', 'localtime')
            WHERE id = ?
        `, [id]);

        if (result.success) {
            res.json({
                success: true,
                message: 'GPU账号使用时间更新成功'
            });
        } else {
            throw new Error('数据库更新失败');
        }

    } catch (error) {
        console.error('更新GPU账号使用时间失败:', error);
        res.status(500).json({
            success: false,
            error: '更新使用时间失败',
            message: error.message
        });
    }
});

/**
 * 删除GPU账号
 */
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        // 检查账号是否存在
        const existingAccount = await database.get(
            'SELECT id FROM gpu_accounts WHERE id = ?',
            [id]
        );

        if (!existingAccount) {
            return res.status(404).json({
                success: false,
                error: '账号不存在'
            });
        }

        // 删除账号
        const result = await database.run(
            'DELETE FROM gpu_accounts WHERE id = ?',
            [id]
        );

        if (result.success) {
            res.json({
                success: true,
                message: 'GPU账号删除成功'
            });
        } else {
            throw new Error('数据库删除失败');
        }

    } catch (error) {
        console.error('删除GPU账号失败:', error);
        res.status(500).json({
            success: false,
            error: '删除账号失败',
            message: error.message
        });
    }
});

/**
 * 获取可用的GPU账号（用于其他模块调用）
 * 注意：此接口与gpu-utils.js中的getAvailableGpuAccount函数功能类似
 * 区别在于：此接口返回多个账户列表，getAvailableGpuAccount返回单个最优账户
 */
router.get('/available/list', async (req, res) => {
    try {
        const { limit = 10 } = req.query;

        const result = await database.all(`
            SELECT id, account, token, api_key
            FROM gpu_accounts
            WHERE status = 'active' AND (
                (token IS NOT NULL AND token != '') OR
                (api_key IS NOT NULL AND api_key != '')
            )
            ORDER BY
                CASE WHEN last_used_at IS NULL THEN 0 ELSE 1 END,
                last_used_at ASC
            LIMIT ?
        `, [parseInt(limit)]);

        const accounts = result.results || [];

        res.json({
            success: true,
            accounts: accounts
        });

    } catch (error) {
        console.error('获取可用GPU账号失败:', error);
        res.status(500).json({
            success: false,
            error: '获取可用账号失败',
            message: error.message
        });
    }
});

/**
 * 批量操作GPU账号状态
 */
router.put('/batch/status', async (req, res) => {
    try {
        const { accountIds, status } = req.body;

        if (!Array.isArray(accountIds) || accountIds.length === 0) {
            return res.status(400).json({
                success: false,
                error: '请选择要操作的账号'
            });
        }

        if (!['active', 'inactive', 'expired'].includes(status)) {
            return res.status(400).json({
                success: false,
                error: '无效的状态值'
            });
        }

        const placeholders = accountIds.map(() => '?').join(',');
        const result = await database.run(`
            UPDATE gpu_accounts 
            SET status = ?, updated_at = datetime('now', 'localtime')
            WHERE id IN (${placeholders})
        `, [status, ...accountIds]);

        if (result.success) {
            res.json({
                success: true,
                message: `成功更新 ${result.meta.changes} 个账号状态`
            });
        } else {
            throw new Error('数据库批量更新失败');
        }

    } catch (error) {
        console.error('批量更新GPU账号状态失败:', error);
        res.status(500).json({
            success: false,
            error: '批量更新失败',
            message: error.message
        });
    }
});

export default router;