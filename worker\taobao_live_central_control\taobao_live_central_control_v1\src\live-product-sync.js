import crypto from 'crypto';
import { database } from './database.js';
import { handleTaobaoApiResponse } from './utils/response-handler.js';

/**
 * 直播产品同步工具
 */
class LiveProductSync {
    constructor() {
        this.appKey = '12574478';
        this.api = 'mtop.taobao.dreamweb.live.item.all.query';
        this.version = '1.0';
    }

    /**
     * MD5哈希函数
     * @param {string} string 
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string, 'utf8').digest('hex');
    }

    /**
     * 生成签名
     * @param {string} h5Token 
     * @param {number} timestamp 
     * @param {object} data 
     * @returns {string}
     */
    generateSign(h5Token, timestamp, data) {
        const dataStr = JSON.stringify(data);
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
        return this.md5(signString);
    }

    /**
     * 构建请求URL
     * @param {string} h5Token 
     * @param {object} data 
     * @returns {object}
     */
    buildRequestUrl(h5Token, data) {
        const timestamp = Date.now();
        const sign = this.generateSign(h5Token, timestamp, data);
        
        const params = new URLSearchParams({
            jsv: '2.7.4',
            appKey: this.appKey,
            t: timestamp.toString(),
            sign: sign,
            api: this.api,
            v: this.version,
            preventFallback: 'true',
            type: 'json',
            dataType: 'json',
            data: JSON.stringify(data)
        });

        return {
            url: `https://h5api.m.taobao.com/h5/${this.api}/${this.version}/?${params.toString()}`,
            timestamp,
            sign
        };
    }

    /**
     * 获取直播产品数据
     * @param {string} h5Token
     * @param {string} fullCookie
     * @param {string} liveId
     * @param {string} anchorName - 主播名称，用于cookie更新
     * @returns {Promise<object>}
     */
    async fetchLiveProducts(h5Token, fullCookie, liveId, anchorName) {
        try {
            const data = {
                liveId: liveId,
                searchType: "all",
                pageNum: 1,
                anchorSource: "pc"
            };

            const { url } = this.buildRequestUrl(h5Token, data);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': '*/*',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Priority': 'u=1, i',
                    'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-site',
                    'Referer': `https://liveplatform.taobao.com/restful/index/home/<USER>/edit/v2?liveId=${liveId}`,
                    'Cookie': fullCookie
                }
            });

            // 使用统一的响应处理
            const responseResult = await handleTaobaoApiResponse(response, anchorName, fullCookie);

            if (!responseResult.success) {
                return {
                    success: false,
                    error: responseResult.error,
                    errorType: responseResult.errorType,
                    cookieUpdated: responseResult.cookieUpdated,
                    data: []
                };
            }

            return {
                success: true,
                data: responseResult.data.itemList || [],
                cookieUpdated: responseResult.cookieUpdated
            };

        } catch (error) {
            console.error('获取直播产品失败:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    /**
     * 解析讲解状态
     * @param {string} auditStatus 审核状态
     * @param {object} timeMovingPlayInfo 讲解播放信息
     * @returns {string}
     */
    parseExplanationStatus(auditStatus, timeMovingPlayInfo) {
        // 解析 timeMovingPlayInfo 中的 isMounting 字段
        let isMounting = false;
        if (timeMovingPlayInfo) {
            try {
                const playInfo = typeof timeMovingPlayInfo === 'string'
                    ? JSON.parse(timeMovingPlayInfo)
                    : timeMovingPlayInfo;
                isMounting = playInfo.isMounting === true;
            } catch (error) {
                console.warn('解析 timeMovingPlayInfo 失败:', error);
            }
        }

        // 如果 isMounting 为 true，代表未讲解（只是同步的旧讲解状态）
        if (isMounting) {
            return '未讲解';
        }

        // 如果auditStatus为空，代表未讲解
        if (!auditStatus || auditStatus === '' || auditStatus === null || auditStatus === undefined) {
            return '未讲解';
        }

        // 根据审核状态判断
        switch (String(auditStatus)) {
            case '1': return '讲解通过';
            case '0': return '讲解未通过';
            default: return '未讲解';
        }
    }

    /**
     * 计算佣金金额
     * @param {number} price 
     * @param {string} commissionRate 
     * @returns {number}
     */
    calculateCommissionAmount(price, commissionRate) {
        if (!price || !commissionRate) return 0;
        
        // 移除百分号并转换为数字
        const rate = parseFloat(commissionRate.replace('%', ''));
        if (isNaN(rate)) return 0;
        
        return (price * rate / 100);
    }

    /**
     * 转换直播产品数据格式
     * @param {array} rawData
     * @param {string} liveId
     * @param {string} anchorName
     * @param {boolean} isNewProduct 是否为新产品
     * @returns {array}
     */
    transformLiveProducts(rawData, liveId, anchorName, isNewProduct = true) {
        if (!Array.isArray(rawData)) {
            return [];
        }

        return rawData.map(item => {
            const price = parseFloat(item.itemPrice) || 0;
            const commissionRate = item.extendVal?.tcpCommission || '';
            const commissionAmount = this.calculateCommissionAmount(price, commissionRate);

            const baseData = {
                live_id: liveId,
                anchor_name: anchorName,
                product_sequence: parseInt(item.goodsIndex) || 0,
                product_id: item.itemId || '',
                product_name: item.itemName || '',
                product_subtitle: item.extendVal?.subTitle || '',
                product_image: item.itemPic ? (item.itemPic.startsWith('http') ? item.itemPic : 'https:' + item.itemPic) : '',
                amount: price,
                commission_rate: parseFloat(commissionRate.replace('%', '')) || 0,
                commission_amount: commissionAmount,
                product_type: item.extendVal?.tcpCommissionType || '',
                product_category: item.extendVal?.categoryLevelOneName || '',
                spot_duration: parseInt(item.extendVal?.spotDuration) || 0,
                explanation_status: this.parseExplanationStatus(
                    item.extendVal?.timeMovingAuditStatus,
                    item.extendVal?.timeMovingPlayInfo
                )
            };

            // 只有新产品才设置这些字段的默认值
            if (isNewProduct) {
                baseData.script_content = '';
                baseData.audio_extraction_status = '未生成';
                baseData.push_status = '未推送';
            }

            return baseData;
        });
    }

    /**
     * 保存直播产品到数据库
     * @param {array} rawData 原始数据
     * @param {string} liveId 直播ID
     * @param {string} anchorName 主播名称
     * @returns {Promise<object>}
     */
    async saveLiveProducts(rawData, liveId, anchorName) {
        if (!Array.isArray(rawData) || rawData.length === 0) {
            return { success: true, inserted: 0, updated: 0, deleted: 0 };
        }

        let inserted = 0;
        let updated = 0;
        let deleted = 0;

        try {
            // 先查询出该直播的所有现有产品
            const existingResult = await database.all(
                'SELECT product_id FROM live_products WHERE live_id = ?',
                [liveId]
            );
            const existingProductIds = new Set(existingResult.results?.map(item => item.product_id) || []);

            // 新产品的ID集合
            const newProductIds = new Set(rawData.map(item => item.itemId || ''));

            // 需要删除的产品ID（存在于数据库但不在新数据中）
            const toDeleteIds = [...existingProductIds].filter(id => !newProductIds.has(id));

            // 开始事务
            await database.exec('BEGIN TRANSACTION');

            try {
                // 处理每个产品
                for (const item of rawData) {
                    const productId = item.itemId || '';
                    const isExisting = existingProductIds.has(productId);

                    // 转换产品数据
                    const productData = this.transformLiveProducts([item], liveId, anchorName, !isExisting)[0];

                    if (isExisting) {
                        // 更新现有记录 - 不更新 script_content, audio_extraction_status, push_status
                        await database.run(`
                            UPDATE live_products SET
                                anchor_name = ?,
                                product_sequence = ?,
                                product_name = ?,
                                product_subtitle = ?,
                                product_image = ?,
                                amount = ?,
                                commission_rate = ?,
                                commission_amount = ?,
                                product_type = ?,
                                product_category = ?,
                                spot_duration = ?,
                                explanation_status = ?,
                                updated_at = datetime('now', '+8 hours')
                            WHERE live_id = ? AND product_id = ?
                        `, [
                            productData.anchor_name,
                            productData.product_sequence,
                            productData.product_name,
                            productData.product_subtitle,
                            productData.product_image,
                            productData.amount,
                            productData.commission_rate,
                            productData.commission_amount,
                            productData.product_type,
                            productData.product_category,
                            productData.spot_duration,
                            productData.explanation_status,
                            productData.live_id,
                            productData.product_id
                        ]);
                        updated++;
                    } else {
                        // 插入新记录 - 包含默认的 script_content, audio_extraction_status, push_status
                        await database.run(`
                            INSERT INTO live_products (
                                live_id, anchor_name, product_sequence, product_id, product_name, product_subtitle,
                                product_image, amount, commission_rate, commission_amount, product_type,
                                product_category, spot_duration, explanation_status, script_content,
                                audio_extraction_status, push_status
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        `, [
                            productData.live_id,
                            productData.anchor_name,
                            productData.product_sequence,
                            productData.product_id,
                            productData.product_name,
                            productData.product_subtitle,
                            productData.product_image,
                            productData.amount,
                            productData.commission_rate,
                            productData.commission_amount,
                            productData.product_type,
                            productData.product_category,
                            productData.spot_duration,
                            productData.explanation_status,
                            productData.script_content,
                            productData.audio_extraction_status,
                            productData.push_status
                        ]);
                        inserted++;
                    }
                }

                // 删除多余的产品
                if (toDeleteIds.length > 0) {
                    const placeholders = toDeleteIds.map(() => '?').join(',');
                    await database.run(
                        `DELETE FROM live_products WHERE live_id = ? AND product_id IN (${placeholders})`,
                        [liveId, ...toDeleteIds]
                    );
                    deleted = toDeleteIds.length;
                }

                // 提交事务
                await database.exec('COMMIT');

                return { success: true, inserted, updated, deleted };

            } catch (error) {
                // 回滚事务
                await database.exec('ROLLBACK');
                throw error;
            }

        } catch (error) {
            console.error('保存直播产品失败:', error);
            return { success: false, error: error.message, inserted, updated, deleted };
        }
    }

    /**
     * 更新直播计划的统计信息
     * @param {string} liveId
     * @returns {Promise<object>}
     */
    async updateLivePlanStats(liveId) {
        try {
            // 查询该直播的所有产品统计信息
            const statsResult = await database.get(`
                SELECT
                    COUNT(*) as product_count,
                    AVG(amount) as average_amount,
                    AVG(commission_amount) as average_commission
                FROM live_products
                WHERE live_id = ?
            `, [liveId]);

            if (statsResult) {
                const stats = statsResult;

                // 更新直播计划表
                await database.run(`
                    UPDATE live_plans SET
                        product_count = ?,
                        average_amount = ?,
                        average_commission = ?,
                        updated_at = datetime('now', '+8 hours')
                    WHERE live_id = ?
                `, [
                    stats.product_count || 0,
                    parseFloat(stats.average_amount) || 0,
                    parseFloat(stats.average_commission) || 0,
                    liveId
                ]);

                return {
                    success: true,
                    stats: {
                        product_count: stats.product_count || 0,
                        average_amount: parseFloat(stats.average_amount) || 0,
                        average_commission: parseFloat(stats.average_commission) || 0
                    }
                };
            }

            return { success: false, error: '未找到统计数据' };

        } catch (error) {
            console.error('更新直播计划统计失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 同步指定直播的产品
     * @param {string} liveId
     * @param {string} anchorName
     * @param {string} h5Token
     * @param {string} fullCookie
     * @returns {Promise<object>}
     */
    async syncLiveProducts(liveId, anchorName, h5Token, fullCookie) {
        try {
            // 获取直播产品数据
            const result = await this.fetchLiveProducts(h5Token, fullCookie, liveId, anchorName);

            if (!result.success) {
                return {
                    success: false,
                    error: result.error,
                    errorType: result.errorType,
                    liveId,
                    inserted: 0,
                    updated: 0,
                    deleted: 0,
                    total: 0
                };
            }

            // 保存到数据库（直接传入原始数据）
            const saveResult = await this.saveLiveProducts(result.data, liveId, anchorName);

            if (!saveResult.success) {
                return {
                    success: false,
                    error: saveResult.error,
                    liveId,
                    inserted: saveResult.inserted,
                    updated: saveResult.updated,
                    deleted: saveResult.deleted,
                    total: result.data.length
                };
            }

            // 更新直播计划统计信息
            const statsResult = await this.updateLivePlanStats(liveId);

            return {
                success: true,
                error: saveResult.error,
                liveId,
                inserted: saveResult.inserted,
                updated: saveResult.updated,
                deleted: saveResult.deleted,
                total: result.data.length,
                stats: statsResult.success ? statsResult.stats : null
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                liveId,
                inserted: 0,
                updated: 0
            };
        }
    }
}

export default LiveProductSync;
