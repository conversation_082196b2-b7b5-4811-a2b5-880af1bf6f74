/**
 * GPU音频生成账户管理
 * 集成象工云GPU服务，实现批量AI音频生成功能
 */

class GpuAccountsManager {
    constructor() {
        this.currentLiveId = null;
        this.availableInstance = null;
        this.isProcessing = false;
        this.initializeModal();
        this.bindEvents();
    }

    // 初始化GPU管理模态框
    initializeModal() {
        const modalHTML = `
            <!-- GPU音频生成模态框 -->
            <div id="gpuAccountsModal" class="fixed z-50 inset-0 overflow-y-auto hidden">
                <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                        
                        <!-- 模态框头部 -->
                        <div class="bg-white px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    <i class="fas fa-microchip mr-2 text-blue-600"></i>批量音频生成
                                </h3>
                                <button type="button" onclick="gpuAccountsManager.hide()" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 任务参数配置 -->
                        <div class="bg-white px-6 py-4">
                            <div class="space-y-4">
                                <!-- 序号范围配置 -->
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">开始序号</label>
                                        <input type="number" id="startSequence" min="1" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                               placeholder="如：1">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">结束序号</label>
                                        <input type="number" id="endSequence" min="1" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                               placeholder="如：10">
                                    </div>
                                </div>

                                <!-- 语速配置 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">语速调节</label>

                                    <!-- 随机语速勾选框 -->
                                    <div class="mb-3">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox" id="randomSpeed" class="form-checkbox h-4 w-4 text-blue-600 rounded">
                                            <span class="ml-2 text-sm text-gray-700">使用随机语速 (1.0, 1.1, 1.2)</span>
                                        </label>
                                    </div>

                                    <!-- 固定语速滑块 -->
                                    <div id="fixedSpeedContainer">
                                        <div class="flex items-center space-x-3">
                                            <input type="range" id="speedSlider" min="0.5" max="2.0" step="0.1" value="1.2"
                                                   class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                            <span id="speedValue" class="text-sm text-gray-600 font-medium w-12">1.2</span>
                                        </div>
                                        <div class="flex justify-between text-xs text-gray-400 mt-1">
                                            <span>慢速(0.5)</span>
                                            <span>正常(1.0)</span>
                                            <span>快速(2.0)</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 并发数配置 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">并发数设置</label>
                                    <div class="flex items-center space-x-3">
                                        <input type="range" id="concurrencySlider" min="1" max="50" step="1" value="20"
                                               class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                        <span id="concurrencyValue" class="text-sm text-gray-600 font-medium w-12">20</span>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-400 mt-1">
                                        <span>单线程(1)</span>
                                        <span>默认(20)</span>
                                        <span>最大(50)</span>
                                    </div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        同时进行的音频生成请求数量，建议根据GPU性能调整
                                    </div>
                                </div>

                                <!-- GPU实例管理 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">GPU实例管理</label>

                                    <!-- 自动创建GPU选项 -->
                                    <div class="mb-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="autoCreateGpu" class="mr-2">
                                            <span class="text-sm text-gray-700">自动创建GPU</span>
                                        </label>
                                        <p class="text-xs text-gray-500 mt-1">勾选后将自动创建GPU实例，无需手动获取</p>
                                    </div>

                                    <div class="flex space-x-2" id="manualInstanceSection">
                                        <input type="text" id="instanceDisplay" readonly
                                               class="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-gray-600"
                                               placeholder="点击获取实例按钮选择GPU实例">
                                        <button type="button" id="getInstanceBtn"
                                                class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <i class="fas fa-search mr-1"></i>获取实例
                                        </button>
                                    </div>
                                    
                                    <!-- 自动销毁选项 -->
                                    <div class="mt-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="autoDestroy" checked
                                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">任务完成后自动销毁实例</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- 状态显示 -->
                                <div id="statusDisplay" class="hidden">
                                    <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                                        <div class="flex items-center">
                                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                            <span class="text-sm text-blue-700" id="statusMessage">准备就绪</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 模态框底部 -->
                        <div class="bg-gray-50 px-6 py-3 flex justify-between items-center">
                            <div class="text-sm text-gray-500">
                                将根据序号范围生成音频文件
                            </div>
                            <div class="flex space-x-2">
                                <button type="button" onclick="gpuAccountsManager.hide()"
                                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    取消
                                </button>
                                <button type="button" id="confirmGenerateBtn"
                                        class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <i class="fas fa-play mr-1"></i>开始生成
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件清理确认模态框 -->
            <div id="fileCleanupModal" class="fixed z-50 inset-0 overflow-y-auto hidden">
                <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
                        
                        <!-- 模态框头部 -->
                        <div class="bg-white px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                <i class="fas fa-exclamation-triangle mr-2 text-yellow-500"></i>检测到已有音频文件
                            </h3>
                        </div>

                        <!-- 内容区域 -->
                        <div class="bg-white px-6 py-4">
                            <p class="text-sm text-gray-600 mb-4" id="fileCleanupMessage">
                                该主播目录下已存在音频文件，是否清空后继续？
                            </p>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                                <div class="flex">
                                    <i class="fas fa-info-circle text-yellow-400 mr-2 mt-0.5"></i>
                                    <div class="text-sm text-yellow-700">
                                        <p class="font-medium mb-1">注意事项：</p>
                                        <ul class="list-disc list-inside space-y-1">
                                            <li>清空：删除所有音频文件（保留.verysync文件）</li>
                                            <li>忽略：保持现有文件，可能出现文件重名</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 模态框底部 -->
                        <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-2">
                            <button type="button" id="ignoreCleanupBtn"
                                    class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <i class="fas fa-arrow-right mr-1"></i>忽略
                            </button>
                            <button type="button" id="confirmCleanupBtn"
                                    class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                                <i class="fas fa-trash-alt mr-1"></i>清空
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        $('body').append(modalHTML);
    }

    // 绑定事件
    bindEvents() {
        // 语速滑块变化
        $('#speedSlider').on('input', (e) => {
            const value = parseFloat(e.target.value);
            $('#speedValue').text(value);
        });

        // 并发数滑块变化
        $('#concurrencySlider').on('input', (e) => {
            const value = parseInt(e.target.value);
            $('#concurrencyValue').text(value);
        });

        // 随机语速勾选框变化
        $('#randomSpeed').on('change', (e) => {
            const isRandomSpeed = e.target.checked;
            if (isRandomSpeed) {
                $('#fixedSpeedContainer').addClass('opacity-50 pointer-events-none');
                $('#speedSlider').prop('disabled', true);
            } else {
                $('#fixedSpeedContainer').removeClass('opacity-50 pointer-events-none');
                $('#speedSlider').prop('disabled', false);
            }
        });

        // 获取GPU实例
        $('#getInstanceBtn').on('click', () => this.getAvailableInstance());

        // 自动创建GPU勾选框事件
        $('#autoCreateGpu').on('change', () => this.handleAutoCreateGpuChange());

        // 确认生成按钮
        $('#confirmGenerateBtn').on('click', () => this.handleConfirmGenerate());

        // 文件清理选择
        $('#confirmCleanupBtn').on('click', () => this.handleFileCleanup(true));
        $('#ignoreCleanupBtn').on('click', () => this.handleFileCleanup(false));

        // 序号输入验证
        $('#startSequence, #endSequence').on('input', () => this.validateSequenceRange());
    }

    // 显示GPU管理模态框
    show(liveId) {
        if (!liveId) {
            layer.msg('直播ID不能为空', { icon: 2 });
            return;
        }

        this.currentLiveId = liveId;
        this.resetModalState();
        $('#gpuAccountsModal').removeClass('hidden');
        this.updateStatus('等待配置参数...');
    }

    // 隐藏模态框
    hide() {
        $('#gpuAccountsModal').addClass('hidden');
        $('#fileCleanupModal').addClass('hidden');
        this.resetModalState();
    }

    // 重置模态框状态
    resetModalState() {
        this.currentLiveId = null;
        this.availableInstance = null;
        this.isProcessing = false;

        // 清空输入
        $('#startSequence, #endSequence').val('');
        $('#speedSlider').val(1.2);
        $('#speedValue').text('1.2');
        $('#concurrencySlider').val(20);
        $('#concurrencyValue').text('20');
        $('#randomSpeed').prop('checked', false);
        $('#fixedSpeedContainer').removeClass('opacity-50 pointer-events-none');
        $('#speedSlider').prop('disabled', false);
        $('#instanceDisplay').val('');
        $('#autoDestroy').prop('checked', true);

        // 隐藏状态
        $('#statusDisplay').addClass('hidden');
        $('#confirmGenerateBtn').prop('disabled', false);
    }

    // 处理自动创建GPU勾选框变化
    handleAutoCreateGpuChange() {
        const isAutoCreate = $('#autoCreateGpu').is(':checked');
        const manualSection = $('#manualInstanceSection');
        const confirmBtn = $('#confirmGenerateBtn');

        if (isAutoCreate) {
            // 隐藏手动获取实例的部分
            manualSection.hide();
            // 清空已选择的实例
            this.availableInstance = null;
            $('#instanceDisplay').val('');

            // 检查是否已输入序号来决定提示信息
            const start = parseInt($('#startSequence').val());
            const end = parseInt($('#endSequence').val());
            if (start && end && start > 0 && end > 0 && start <= end) {
                const count = end - start + 1;
                this.updateStatus(`将处理 ${count} 个产品的音频生成（自动创建GPU）`, 'info');
            } else {
                this.updateStatus('已启用自动创建GPU，请输入序号范围', 'info');
            }
        } else {
            // 显示手动获取实例的部分
            manualSection.show();
            this.updateStatus('请手动获取GPU实例', 'info');
        }

        // 重新验证序号范围以更新按钮状态
        this.validateSequenceRange();
    }

    // 验证序号范围
    validateSequenceRange() {
        const start = parseInt($('#startSequence').val());
        const end = parseInt($('#endSequence').val());
        const confirmBtn = $('#confirmGenerateBtn');
        const isAutoCreate = $('#autoCreateGpu').is(':checked');

        // 首先检查序号是否有效
        if (!start || !end || start <= 0 || end <= 0) {
            if (isAutoCreate) {
                this.updateStatus('已启用自动创建GPU，请输入序号范围', 'info');
            } else {
                this.updateStatus('请输入有效的序号范围', 'warning');
            }
            confirmBtn.prop('disabled', true);
            return false;
        }

        if (start > end) {
            this.updateStatus('开始序号不能大于结束序号', 'error');
            confirmBtn.prop('disabled', true);
            return false;
        }

        // 序号有效，检查GPU实例要求
        if (isAutoCreate) {
            // 自动创建GPU模式，不需要检查实例
            const count = end - start + 1;
            this.updateStatus(`将处理 ${count} 个产品的音频生成（自动创建GPU）`, 'info');
            confirmBtn.prop('disabled', false);
            return true;
        } else if (this.availableInstance) {
            // 手动模式且已有实例
            const count = end - start + 1;
            this.updateStatus(`将处理 ${count} 个产品的音频生成`, 'info');
            confirmBtn.prop('disabled', false);
            return true;
        } else {
            // 手动模式但没有实例
            this.updateStatus('请先获取GPU实例', 'warning');
            confirmBtn.prop('disabled', true);
            return false;
        }
    }

    // 获取可用GPU实例
    async getAvailableInstance() {
        const btn = $('#getInstanceBtn');
        const originalText = btn.html();
        
        try {
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>获取中...');
            this.updateStatus('正在获取可用GPU实例...', 'info');

            // 获取当前直播ID和主播信息
            const liveInfo = this.getCurrentLiveInfo();
            if (!liveInfo.liveId) {
                throw new Error('无法获取当前直播ID，请确保已选择直播场次');
            }

            // 调用后端API获取实例列表
            const response = await fetch('/api/gpu/instances', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getApiKeyHeader()
                },
                body: JSON.stringify({
                    liveId: liveInfo.liveId,
                    anchorName: liveInfo.anchorName
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success && result.instance) {
                // 获取到可用实例
                this.availableInstance = result.instance;
                
                // 显示实例信息
                const displayText = `${this.availableInstance.public_id} (${this.availableInstance.gpu_model})`;
                $('#instanceDisplay').val(displayText);
                
                this.updateStatus(`已获取GPU实例: ${this.availableInstance.gpu_model}`, 'success');

                // 重新验证序号范围以启用确认按钮
                this.validateSequenceRange();

            } else {
                this.updateStatus(result.error || '暂无可用GPU实例，请稍后重试', 'error');
                $('#instanceDisplay').val('');
                this.availableInstance = null;
            }

        } catch (error) {
            console.error('获取GPU实例失败:', error);
            this.updateStatus('获取GPU实例失败: ' + error.message, 'error');
            $('#instanceDisplay').val('');
            this.availableInstance = null;
        } finally {
            btn.prop('disabled', false).html(originalText);
        }
    }

    // 处理确认生成
    async handleConfirmGenerate() {
        // 验证参数
        if (!this.validateSequenceRange()) {
            layer.msg('请检查序号范围设置', { icon: 2 });
            return;
        }

        // 检查GPU实例要求（考虑自动创建GPU的情况）
        const isAutoCreate = $('#autoCreateGpu').is(':checked');
        if (!isAutoCreate && !this.availableInstance) {
            layer.msg('请先获取GPU实例', { icon: 2 });
            return;
        }

        if (this.isProcessing) {
            layer.msg('任务正在处理中，请稍候', { icon: 0 });
            return;
        }

        // 获取当前直播信息
        const liveInfo = this.getCurrentLiveInfo();
        
        // 获取主播名称用于检查文件目录
        const anchorName = isAutoCreate ?
            (liveInfo.anchorName || '主播') :
            (this.availableInstance ? (this.availableInstance.anchor_name || liveInfo.anchorName || '主播') : (liveInfo.anchorName || '主播'));
        
        // 检查目标目录是否有文件
        const hasExistingFiles = await this.checkDirectoryFiles(anchorName);
        
        if (hasExistingFiles.exists) {
            // 显示文件清理确认框
            this.showFileCleanupModal(hasExistingFiles.count, anchorName);
        } else {
            // 直接开始任务创建
            this.startAudioGenerationTask();
        }
    }

    // 检查目录文件
    async checkDirectoryFiles(anchorName) {
        try {
            const response = await fetch('/api/gpu/check-directory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getApiKeyHeader()
                },
                body: JSON.stringify({
                    anchorName: anchorName
                })
            });

            if (!response.ok) {
                console.warn('检查目录失败，继续执行任务');
                return { exists: false, count: 0 };
            }

            const result = await response.json();
            return {
                exists: result.hasFiles || false,
                count: result.fileCount || 0
            };

        } catch (error) {
            console.warn('检查目录异常:', error);
            return { exists: false, count: 0 };
        }
    }

    // 显示文件清理确认模态框
    showFileCleanupModal(fileCount, anchorName) {
        const message = `该主播目录下已存在 ${fileCount} 个音频文件，是否清空后继续？`;
        $('#fileCleanupMessage').text(message);
        $('#fileCleanupModal').removeClass('hidden');
    }

    // 处理文件清理选择
    async handleFileCleanup(shouldCleanup) {
        $('#fileCleanupModal').addClass('hidden');

        if (shouldCleanup) {
            // 执行清理操作
            const success = await this.cleanupDirectory();
            if (!success) {
                layer.msg('清理文件失败，任务已取消', { icon: 2 });
                return;
            }
            layer.msg('文件清理完成，开始生成任务', { icon: 1 });
        }

        // 开始音频生成任务
        this.startAudioGenerationTask();
    }

    // 清理目录文件
    async cleanupDirectory() {
        try {
            const liveInfo = this.getCurrentLiveInfo();
            const anchorName = (this.availableInstance ? this.availableInstance.anchor_name : null) || liveInfo.anchorName || '主播';
            
            const response = await fetch('/api/gpu/cleanup-directory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getApiKeyHeader()
                },
                body: JSON.stringify({
                    anchorName: anchorName
                })
            });

            if (!response.ok) {
                throw new Error('清理目录失败');
            }

            const result = await response.json();
            return result.success || false;

        } catch (error) {
            console.error('清理目录失败:', error);
            return false;
        }
    }

    // 开始音频生成任务
    async startAudioGenerationTask() {
        if (this.isProcessing) return;

        this.isProcessing = true;
        const confirmBtn = $('#confirmGenerateBtn');
        confirmBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>创建任务中...');

        try {
            // 获取当前直播信息
            const liveInfo = this.getCurrentLiveInfo();
            if (!liveInfo.liveId) {
                throw new Error('无法获取当前直播ID');
            }

            // 收集任务参数
            const isRandomSpeed = $('#randomSpeed').is(':checked');
            const isAutoCreate = $('#autoCreateGpu').is(':checked');
            const taskParams = {
                liveId: liveInfo.liveId,
                startSequence: parseInt($('#startSequence').val()),
                endSequence: parseInt($('#endSequence').val()),
                speed: isRandomSpeed ? 'random' : parseFloat($('#speedSlider').val()),
                randomSpeed: isRandomSpeed,
                autoDestroy: $('#autoDestroy').is(':checked'),
                concurrency: parseInt($('#concurrencySlider').val()),
                autoCreateGpu: isAutoCreate,
                instanceId: isAutoCreate ? null : (this.availableInstance ? this.availableInstance.id : null),
                publicId: isAutoCreate ? null : (this.availableInstance ? this.availableInstance.public_id : null),
                anchorName: isAutoCreate ? (liveInfo.anchorName || '主播') : (this.availableInstance ? (this.availableInstance.anchor_name || liveInfo.anchorName || '主播') : (liveInfo.anchorName || '主播'))
            };

            this.updateStatus('正在创建批量音频生成任务...', 'info');

            // 调用后端API创建任务
            const response = await fetch('/api/gpu/audio-generation/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getApiKeyHeader()
                },
                body: JSON.stringify(taskParams)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                const productCount = taskParams.endSequence - taskParams.startSequence + 1;

                layer.msg(`批量音频生成任务创建成功！<br>任务ID: ${result.taskId}<br>产品数量: ${productCount}<br>并发数: ${taskParams.concurrency}`, {
                    icon: 1,
                    time: 5000
                });

                this.updateStatus(`任务创建成功，处理 ${productCount} 个产品，并发数: ${taskParams.concurrency}`, 'success');

                // 询问是否打开任务管理页面
                setTimeout(() => {
                    layer.confirm('任务已创建，是否打开任务管理页面查看进度？', {
                        btn: ['打开任务管理', '稍后查看']
                    }, function(index) {
                        window.open('/task-manager.html', '_blank');
                        layer.close(index);
                    });
                }, 1000);

                // 延迟关闭模态框
                setTimeout(() => {
                    this.hide();
                }, 2000);

            } else {
                throw new Error(result.error || '任务创建失败');
            }

        } catch (error) {
            console.error('创建音频生成任务失败:', error);
            this.updateStatus('任务创建失败: ' + error.message, 'error');
            layer.msg('创建音频生成任务失败: ' + error.message, { icon: 2 });
        } finally {
            this.isProcessing = false;
            confirmBtn.prop('disabled', false).html('<i class="fas fa-play mr-1"></i>开始生成');
        }
    }

    // 更新状态显示
    updateStatus(message, type = 'info') {
        const statusDisplay = $('#statusDisplay');
        const statusMessage = $('#statusMessage');
        
        // 显示状态区域
        statusDisplay.removeClass('hidden');
        
        // 更新样式和图标
        const statusConfig = {
            info: { class: 'bg-blue-50 border-blue-200 text-blue-700', icon: 'fas fa-info-circle text-blue-500' },
            success: { class: 'bg-green-50 border-green-200 text-green-700', icon: 'fas fa-check-circle text-green-500' },
            warning: { class: 'bg-yellow-50 border-yellow-200 text-yellow-700', icon: 'fas fa-exclamation-triangle text-yellow-500' },
            error: { class: 'bg-red-50 border-red-200 text-red-700', icon: 'fas fa-exclamation-circle text-red-500' }
        };

        const config = statusConfig[type] || statusConfig.info;
        
        statusDisplay.attr('class', `${config.class} border rounded-md p-3`);
        statusMessage.parent().html(`
            <i class="${config.icon} mr-2"></i>
            <span class="text-sm ${config.class.split(' ')[2]}" id="statusMessage">${message}</span>
        `);
    }

    // 获取API密钥头部
    getApiKeyHeader() {
        if (typeof addApiKeyHeader === 'function') {
            return addApiKeyHeader();
        }
        
        const apiKey = this.getCookie('api_key');
        return apiKey ? { 'X-API-Key': apiKey } : {};
    }

    // 获取Cookie
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    // 获取当前直播信息
    getCurrentLiveInfo() {
        // 尝试从产品列表模态框获取信息
        if (typeof productListModal !== 'undefined' && productListModal.currentLiveId) {
            return {
                liveId: productListModal.currentLiveId,
                anchorName: this.getAnchorName()
            };
        }

        // 尝试从页面URL或其他来源获取
        const urlParams = new URLSearchParams(window.location.search);
        const liveId = urlParams.get('liveId') || this.currentLiveId;
        
        if (liveId) {
            return {
                liveId: liveId,
                anchorName: this.getAnchorName()
            };
        }

        return { liveId: null, anchorName: null };
    }

    /**
     * 统一的主播名称获取策略
     * 按优先级顺序尝试获取主播名称：
     * 1. 产品数据中的主播名称
     * 2. 页面标题中的主播名称
     * 3. 默认值"主播"
     * @returns {string} 主播名称
     */
    getAnchorName() {
        // 优先级1: 从产品数据中获取
        const anchorFromProducts = this.getAnchorNameFromProducts();
        if (anchorFromProducts && anchorFromProducts !== '主播') {
            return anchorFromProducts;
        }

        // 优先级2: 从页面标题中获取
        const anchorFromPage = this.getAnchorNameFromPage();
        if (anchorFromPage && anchorFromPage !== '主播') {
            return anchorFromPage;
        }

        // 优先级3: 默认值
        return '主播';
    }

    // 从产品数据中获取主播名称
    getAnchorNameFromProducts() {
        try {
            if (typeof productListModal !== 'undefined' &&
                productListModal.products &&
                productListModal.products.length > 0) {
                const anchorName = productListModal.products[0].anchor_name;
                return anchorName && anchorName.trim() ? anchorName.trim() : null;
            }
        } catch (error) {
            console.warn('从产品数据获取主播名称失败:', error);
        }
        return null;
    }

    // 从页面中获取主播名称
    getAnchorNameFromPage() {
        try {
            // 尝试从页面标题或其他元素获取主播名称
            const titleElement = $('#product-list-modal-title');
            if (titleElement.length > 0) {
                const titleText = titleElement.text();
                const match = titleText.match(/产品列表 - (.+)/);
                if (match) {
                    const anchorName = match[1].split('-')[0].trim();
                    return anchorName && anchorName !== '主播' ? anchorName : null;
                }
            }
        } catch (error) {
            console.warn('从页面标题获取主播名称失败:', error);
        }
        return null;
    }
}

// 创建全局实例
const gpuAccountsManager = new GpuAccountsManager();

// 全局函数供其他模块调用
function showGpuAccountsModal(liveId) {
    gpuAccountsManager.show(liveId);
}

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GpuAccountsManager, gpuAccountsManager };
}