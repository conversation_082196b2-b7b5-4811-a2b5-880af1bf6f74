/**
 * 通用工具模块
 * 包含时间格式化、错误处理、状态映射等常用函数
 */

/**
 * 格式化日期时间
 * @param {Date|string|null} date 日期对象或字符串
 * @param {string} format 格式类型：'api'|'db'|'display'
 * @returns {string} 格式化后的时间字符串
 */
export function formatDateTime(date = null, format = 'display') {
    if (!date) return '';
    
    const currentTime = date instanceof Date ? date : new Date(date);
    
    if (isNaN(currentTime.getTime())) {
        return '';
    }
    
    const year = currentTime.getFullYear();
    const month = String(currentTime.getMonth() + 1).padStart(2, '0');
    const day = String(currentTime.getDate()).padStart(2, '0');
    const hours = String(currentTime.getHours()).padStart(2, '0');
    const minutes = String(currentTime.getMinutes()).padStart(2, '0');
    const seconds = String(currentTime.getSeconds()).padStart(2, '0');

    switch (format) {
        case 'api':
            return `${year}${month}${day} ${hours}:${minutes}:${seconds}`;
        case 'db':
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        case 'display':
        default:
            return currentTime.toLocaleString('zh-CN');
    }
}

/**
 * 获取当前时间字符串
 * @param {string} format 格式类型
 * @returns {string} 当前时间字符串
 */
export function getCurrentTimeString(format = 'db') {
    return formatDateTime(new Date(), format);
}

/**
 * 任务状态映射
 */
export const TASK_STATUS_MAP = {
    'pending': '待执行',
    'running': '执行中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消',
    'paused': '已暂停'
};

/**
 * 任务类型映射
 */
export const TASK_TYPE_MAP = {
    'hand_card': '手卡提取',
    'audio': '音频生成',
    'live': '直播任务'
};

/**
 * 获取任务状态文本
 * @param {string} status 状态值
 * @returns {string} 状态文本
 */
export function getTaskStatusText(status) {
    return TASK_STATUS_MAP[status] || status;
}

/**
 * 获取任务类型文本
 * @param {string} type 类型值
 * @returns {string} 类型文本
 */
export function getTaskTypeText(type) {
    return TASK_TYPE_MAP[type] || type;
}

/**
 * 统一的API响应格式
 * @param {boolean} success 是否成功
 * @param {any} data 数据
 * @param {string} message 消息
 * @param {string} error 错误信息
 * @returns {Object} 标准API响应格式
 */
export function createApiResponse(success, data = null, message = '', error = '') {
    const response = { success };
    
    if (data !== null) response.data = data;
    if (message) response.message = message;
    if (error) response.error = error;
    
    return response;
}

/**
 * 错误处理包装器
 * @param {Function} fn 要包装的异步函数
 * @param {string} errorPrefix 错误前缀
 * @returns {Function} 包装后的函数
 */
export function withErrorHandling(fn, errorPrefix = '操作失败') {
    return async (...args) => {
        try {
            return await fn(...args);
        } catch (error) {
            console.error(`${errorPrefix}:`, error);
            throw error;
        }
    };
}

/**
 * 延迟函数
 * @param {number} ms 延迟毫秒数
 * @returns {Promise} Promise对象
 */
export function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 批量处理数组
 * @param {Array} array 要处理的数组
 * @param {number} batchSize 批次大小
 * @param {Function} processor 处理函数
 * @param {number} delay 批次间延迟（毫秒）
 * @returns {Promise<Array>} 处理结果
 */
export async function processBatches(array, batchSize, processor, delay = 0) {
    const results = [];
    
    for (let i = 0; i < array.length; i += batchSize) {
        const batch = array.slice(i, i + batchSize);
        const batchResults = await processor(batch);
        results.push(...batchResults);
        
        if (delay > 0 && i + batchSize < array.length) {
            await sleep(delay);
        }
    }
    
    return results;
}

/**
 * 验证必填字段
 * @param {Object} data 数据对象
 * @param {Array<string>} requiredFields 必填字段列表
 * @returns {Array<string>} 缺失的字段列表
 */
export function validateRequiredFields(data, requiredFields) {
    const missing = [];
    
    for (const field of requiredFields) {
        if (data[field] === undefined || data[field] === null || data[field] === '') {
            missing.push(field);
        }
    }
    
    return missing;
}

/**
 * 安全的JSON解析
 * @param {string} jsonString JSON字符串
 * @param {any} defaultValue 默认值
 * @returns {any} 解析结果或默认值
 */
export function safeJsonParse(jsonString, defaultValue = null) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.warn('JSON解析失败:', error);
        return defaultValue;
    }
}

/**
 * 生成随机字符串
 * @param {number} length 长度
 * @returns {string} 随机字符串
 */
export function generateRandomString(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝结果
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    
    if (typeof obj === 'object') {
        const copy = {};
        Object.keys(obj).forEach(key => {
            copy[key] = deepClone(obj[key]);
        });
        return copy;
    }
    
    return obj;
}

/**
 * 限流器
 * @param {Function} fn 要限流的函数
 * @param {number} limit 并发限制
 * @returns {Function} 限流后的函数
 */
export function throttle(fn, limit) {
    let running = 0;
    const queue = [];
    
    return function(...args) {
        return new Promise((resolve, reject) => {
            queue.push({ args, resolve, reject });
            processQueue();
        });
    };
    
    async function processQueue() {
        if (running >= limit || queue.length === 0) {
            return;
        }
        
        running++;
        const { args, resolve, reject } = queue.shift();
        
        try {
            const result = await fn(...args);
            resolve(result);
        } catch (error) {
            reject(error);
        } finally {
            running--;
            processQueue();
        }
    }
} 