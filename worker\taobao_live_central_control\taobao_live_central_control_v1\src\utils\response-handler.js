/**
 * 统一的淘宝API响应处理工具
 */

import { parseSetCookieFromResponse, updateAnchorCookie } from './cookie-utils.js';

/**
 * 统一处理淘宝API响应，自动处理cookie更新
 * @param {Response} response - fetch响应对象
 * @param {string} anchorName - 主播名称
 * @param {string} originalCookie - 原始cookie字符串
 * @returns {Promise<object>} - 处理后的响应结果
 */
export async function handleTaobaoApiResponse(response, anchorName, originalCookie) {
  try {
    // 检查响应状态
    if (!response.ok) {
      return {
        success: false,
        error: `HTTP错误: ${response.status} ${response.statusText}`,
        errorType: 'HTTP_ERROR',
        cookieUpdated: false
      };
    }

    // 解析响应数据
    const result = await response.json();
    
    // 检查是否有新的cookie需要更新
    let cookieUpdated = false;
    let updatedCookie = originalCookie;

    const cookieInfo = parseSetCookieFromResponse(response);
    if (cookieInfo.hasNewCookies) {
      console.log(`🍪 检测到新cookie，准备更新主播 ${anchorName} 的cookie信息`);
      console.log('新cookie内容:', cookieInfo.cookies);

      // 打印新cookie详情
      Object.entries(cookieInfo.cookies).forEach(([name, value]) => {
        console.log(`   📝 新cookie: ${name}=${value}`);
      });

      updatedCookie = await updateAnchorCookie(anchorName, cookieInfo.cookies, originalCookie);
      cookieUpdated = true;

      console.log(`✅ 主播 ${anchorName} 的cookie已更新完成`);
    }

    // 检查API调用结果
    if (result.ret && Array.isArray(result.ret) && result.ret.length > 0) {
      const retMsg = result.ret[0];
      
      if (retMsg === 'SUCCESS::调用成功') {
        // 成功情况
        return {
          success: true,
          data: result.data || {},
          cookieUpdated,
          updatedCookie
        };
      } else if (retMsg.includes('FAIL_SYS_TOKEN_EMPTY') && retMsg.includes('令牌为空')) {
        // 令牌为空错误，检查是否有新cookie
        if (cookieUpdated) {
          console.log(`🔑 检测到令牌为空错误，但已更新cookie，主播: ${anchorName}`);
          console.log(`🔄 建议使用新cookie重试请求`);
          return {
            success: false,
            error: retMsg,
            errorType: 'TOKEN_EMPTY_BUT_COOKIE_UPDATED',
            cookieUpdated: true,
            updatedCookie,
            shouldRetry: true // 建议重试
          };
        } else {
          console.log(`❌ 检测到令牌为空错误，但未获取到新cookie，主播: ${anchorName}`);
          return {
            success: false,
            error: retMsg,
            errorType: 'TOKEN_EMPTY',
            cookieUpdated: false
          };
        }
      } else if (retMsg.includes('FAIL_SYS_TOKEN') || retMsg.includes('令牌') || retMsg.includes('FAIL_SYS_SESSION')) {
        // 令牌过期错误
        return {
          success: false,
          error: retMsg,
          errorType: 'TOKEN_EXPIRED',
          cookieUpdated
        };
      } else {
        // 其他API错误
        return {
          success: false,
          error: `API错误: ${retMsg}`,
          errorType: 'API_ERROR',
          cookieUpdated
        };
      }
    }

    // 检查数据格式
    if (result.api && result.data) {
      return {
        success: true,
        data: result.data,
        cookieUpdated,
        updatedCookie
      };
    } else {
      return {
        success: false,
        error: '响应数据格式错误',
        errorType: 'FORMAT_ERROR',
        cookieUpdated
      };
    }

  } catch (error) {
    console.error('处理淘宝API响应失败:', error);
    return {
      success: false,
      error: error.message,
      errorType: 'PARSE_ERROR',
      cookieUpdated: false
    };
  }
}

/**
 * 处理JSONP格式的淘宝API响应
 * @param {string} responseText - 响应文本
 * @param {string} anchorName - 主播名称
 * @param {Response} response - fetch响应对象（用于获取set-cookie）
 * @param {string} originalCookie - 原始cookie字符串
 * @returns {Promise<object>} - 处理后的响应结果
 */
export async function handleTaobaoJsonpResponse(responseText, anchorName, response, originalCookie) {
  try {
    // 检查是否有新的cookie需要更新
    let cookieUpdated = false;
    let updatedCookie = originalCookie;
    
    const cookieInfo = parseSetCookieFromResponse(response);
    if (cookieInfo.hasNewCookies) {
      console.log(`🍪 检测到新cookie，准备更新主播 ${anchorName} 的cookie信息`);
      console.log('新cookie内容:', cookieInfo.cookies);

      // 打印新cookie详情
      Object.entries(cookieInfo.cookies).forEach(([name, value]) => {
        console.log(`   📝 新cookie: ${name}=${value}`);
      });

      updatedCookie = await updateAnchorCookie(anchorName, cookieInfo.cookies, originalCookie);
      cookieUpdated = true;

      console.log(`✅ 主播 ${anchorName} 的cookie已更新完成`);
    }

    // 解析JSONP响应
    const match = responseText.match(/mtopjsonp\d+\((.*)\)/);
    if (!match) {
      return {
        success: false,
        error: '响应格式不匹配JSONP格式',
        errorType: 'FORMAT_ERROR',
        cookieUpdated
      };
    }

    const data = JSON.parse(match[1]);
    console.log(`JSONP解析结果:`, data);

    if (data.ret && data.ret[0].includes('SUCCESS')) {
      const result = data.data?.result || null;
      console.log(`JSONP调用成功: ${result ? result.substring(0, 20) + '...' : 'null'}`);
      return {
        success: true,
        data: result,
        cookieUpdated,
        updatedCookie
      };
    } else {
      const errorMsg = data.ret ? data.ret[0] : '未知错误';
      console.log(`JSONP API返回失败: ${errorMsg}`);

      if (errorMsg.includes('FAIL_SYS_TOKEN_EMPTY') && errorMsg.includes('令牌为空')) {
        // 令牌为空错误，检查是否有新cookie
        if (cookieUpdated) {
          console.log(`🔑 检测到令牌为空错误，但已更新cookie，主播: ${anchorName}`);
          console.log(`🔄 建议使用新cookie重试请求`);
          return {
            success: false,
            error: errorMsg,
            errorType: 'TOKEN_EMPTY_BUT_COOKIE_UPDATED',
            cookieUpdated: true,
            updatedCookie,
            shouldRetry: true
          };
        } else {
          console.log(`❌ 检测到令牌为空错误，但未获取到新cookie，主播: ${anchorName}`);
          return {
            success: false,
            error: errorMsg,
            errorType: 'TOKEN_EMPTY',
            cookieUpdated: false
          };
        }
      } else if (errorMsg.includes('FAIL_SYS_TOKEN') || errorMsg.includes('令牌') || errorMsg.includes('FAIL_SYS_SESSION_EXPIRED')) {
        return {
          success: false,
          error: errorMsg,
          errorType: 'TOKEN_EXPIRED',
          cookieUpdated
        };
      } else {
        return {
          success: false,
          error: errorMsg,
          errorType: 'API_ERROR',
          cookieUpdated
        };
      }
    }

  } catch (error) {
    console.error('处理JSONP响应失败:', error);
    return {
      success: false,
      error: error.message,
      errorType: 'PARSE_ERROR',
      cookieUpdated: false
    };
  }
}

/**
 * 检查响应是否需要重试
 * @param {object} responseResult - 响应处理结果
 * @returns {boolean} - 是否应该重试
 */
export function shouldRetryRequest(responseResult) {
  return responseResult.errorType === 'TOKEN_EMPTY_BUT_COOKIE_UPDATED' && responseResult.shouldRetry;
}

/**
 * 打印cookie更新信息
 * @param {string} anchorName - 主播名称
 * @param {object} cookieInfo - cookie信息
 */
export function logCookieUpdate(anchorName, cookieInfo) {
  if (cookieInfo.hasNewCookies) {
    console.log(`🍪 主播 ${anchorName} 获得新cookie:`);
    Object.entries(cookieInfo.cookies).forEach(([name, value]) => {
      console.log(`   ${name}=${value}`);
    });
  }
}


