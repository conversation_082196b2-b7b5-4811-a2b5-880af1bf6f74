1.获取roomNum

fetch("https://h5api.m.taobao.com/h5/mtop.taobao.dreamweb.room.list/1.0/?jsv=2.7.4&appKey=********&t=1753514880676&sign=fd1ac1d3b4ecd3ca9b779993e4243419&api=mtop.taobao.dreamweb.room.list&v=1.0&preventFallback=true&type=jsonp&dataType=jsonp&callback=mtopjsonp3683&data=%7B%7D", {
  "headers": {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "script",
    "sec-fetch-mode": "no-cors",
    "sec-fetch-site": "same-site"
  },
  "referrer": "https://liveplatform.taobao.com/restful/index/live/list",
  "referrerPolicy": "no-referrer-when-downgrade",
  "body": null,
  "method": "GET",
  "mode": "cors",
  "credentials": "include"
});
响应：
mtopjsonp3683({
    "api": "mtop.taobao.dreamweb.room.list",
    "data": {
        "roomLimit": "12",
        "rooms": [
            {
                "accountId": "*************",
                "extInfoVersion": "0",
                "gmtCreate": "2025-05-15 22:31:23",
                "gmtModified": "2025-05-15 22:31:23",
                "id": "6055818",
                "name": "主直播间【默认】",
                "roomNum": "*********",
                "status": "1",
                "type": "1"
            }
        ]
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "traceId": "2150451017535148806552588e1bb4",
    "v": "1.0"
})



2.根据获取的roomNum，查询信息
fetch("https://h5api.m.taobao.com/h5/mtop.taobao.dreamweb.live.memory.query/1.0/?jsv=2.7.4&appKey=********&t=*************&sign=8451a3828a1e29448727d814ee7cd129&api=mtop.taobao.dreamweb.live.memory.query&v=1.0&preventFallback=true&type=jsonp&dataType=jsonp&callback=mtopjsonp49&data=%7B%22roomNum%22%3A%***********%22%2C%22platform%22%3A%22Web%22%7D", {
  "headers": {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "script",
    "sec-fetch-mode": "no-cors",
    "sec-fetch-site": "same-site"
  },
  "referrer": "https://liveplatform.taobao.com/restful/index/live/list",
  "referrerPolicy": "no-referrer-when-downgrade",
  "body": null,
  "method": "GET",
  "mode": "cors",
  "credentials": "include"
});
响应：
mtopjsonp3686({
    "api": "mtop.taobao.dreamweb.live.memory.query",
    "data": {
        "liveChannelId": "11",
        "liveChannelName": "乐活时光",
        "liveColumnId": "878",
        "liveColumnName": "直播新秀",
        "pitCoverList": [
            {
                "coverRatioUrlMap": {
                    "imgUrl": "https://gw.alicdn.com/tfscom/i4/O1CN01ofue4x1be0wI1QxiY_!!4611686018427382913-0-dgshop.jpg",
                    "imgUrl_3x4": "https://gw.alicdn.com/tfscom/i4/O1CN010E7bmb1be0wIXvOo6_!!4611686018427382913-0-dgshop.jpg"
                },
                "index": "0",
                "mainCover": "true"
            }
        ],
        "showDigitalButton": "false",
        "showLocation": "true",
        "syncPreview": "true",
        "title": "当季新款 时尚美丽"
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "traceId": "2150451017535148807752594e1bb4",
    "v": "1.0"
})

3.根据第二步查询的信息，创建直播计划

fetch("https://h5api.m.taobao.com/h5/mtop.taobao.dreamweb.live.createpre/2.0/?jsv=2.7.4&appKey=********&t=1753515071996&sign=33379aa08e82f9941d942a332fc16149&api=mtop.taobao.dreamweb.live.createpre&v=2.0&type=originaljson&dataType=json&preventFallback=true", {
  "headers": {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/x-www-form-urlencoded",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-site"
  },
  "referrer": "https://liveplatform.taobao.com/restful/index/live/list",
  "referrerPolicy": "no-referrer-when-downgrade",
  "body": "data=%7B%22roomNum%22%3A%***********%22%2C%22landscape%22%3Afalse%2C%22title%22%3A%22%E5%B0%BD%E4%BA%AB%E4%BC%98%E8%B4%A8%E7%94%9F%E6%B4%BB%22%2C%22descInfo%22%3A%22%22%2C%22coverImg%22%3A%22https%3A%2F%2Fgw.alicdn.com%2Ftfscom%2Fi1%2FO1CN01Ph90ae1bo5f3vlEev_!!4611686018427380199-2-dgshop.png%22%2C%22useCustomPullUrl%22%3Afalse%2C%22customPullUrl%22%3A%22%22%2C%22onlyPrivacy%22%3Afalse%2C%22roomType%22%3A%220%22%2C%22appId%22%3A%22%22%2C%22notice%22%3A%22%22%2C%22uploadId%22%3A%22%22%2C%22uploadStr%22%3A%22%22%2C%22videoCoverPicUrl%22%3A%22%22%2C%22coverVideoImg%22%3A%22%22%2C%22showLocation%22%3A%22false%22%2C%22digitalAnchorLive%22%3A%22false%22%2C%22appointmentTime%22%3A%221753545356000%22%2C%22liveEndTime%22%3A%221753559756000%22%2C%22itemListJson%22%3A%22%5B%5D%22%2C%22trialBroadcastFlag%22%3Afalse%2C%22trialBroadcastWhitelist%22%3A%22%5B%5D%22%2C%22extParamMapJson%22%3A%22%7B%5C%22cover_3x4%5C%22%3A%5C%22https%3A%2F%2Fgw.alicdn.com%2Ftfscom%2Fi1%2FO1CN01Ph90ae1bo5f3vlEev_!!4611686018427380199-2-dgshop.png%5C%22%7D%22%2C%22syncPreview%22%3Atrue%2C%22previewTitle%22%3A%22%E5%B0%BD%E4%BA%AB%E4%BC%98%E8%B4%A8%E7%94%9F%E6%B4%BB%22%2C%22previewTime%22%3A1753545356000%2C%22previewCover%22%3A%22https%3A%2F%2Fgw.alicdn.com%2Ftfscom%2Fi1%2FO1CN01Ph90ae1bo5f3vlEev_!!4611686018427380199-2-dgshop.png%22%7D",
  "method": "POST",
  "mode": "cors",
  "credentials": "include"
});
body:
{"roomNum":"*********","landscape":false,"title":"当季新款 时尚美丽","descInfo":"","coverImg":"https://gw.alicdn.com/tfscom/i4/O1CN01ofue4x1be0wI1QxiY_!!4611686018427382913-0-dgshop.jpg","useCustomPullUrl":false,"customPullUrl":"","onlyPrivacy":false,"roomType":"0","appId":"","notice":"","uploadId":"","uploadStr":"","videoCoverPicUrl":"","coverVideoImg":"","showLocation":"true","digitalAnchorLive":"false","appointmentTime":"1753545359900","liveEndTime":"1753559759900","itemListJson":"[]","extParamMapJson":"{\"cover_3x4\":\"https://gw.alicdn.com/tfscom/i4/O1CN010E7bmb1be0wIXvOo6_!!4611686018427382913-0-dgshop.jpg\"}","syncPreview":true,"previewTitle":"当季新款 时尚美丽","previewTime":1753545359900,"previewCover":"https://gw.alicdn.com/tfscom/i4/O1CN01ofue4x1be0wI1QxiY_!!4611686018427382913-0-dgshop.jpg"}



响应
{
    "api": "mtop.taobao.dreamweb.live.createpre",
    "data": {
        "liveId": 528109540462
    },
    "ret": [
        "SUCCESS::调用成功"
    ],
    "traceId": "2150406d17535150719616388e1bb3",
    "v": "2.0"
}




隐藏场次
fetch("https://h5api.m.taobao.com/h5/mtop.taobao.tblive.portal.live.replay.hide.set/1.0/?jsv=2.7.4&appKey=********&t=1753602826635&sign=bb124881b2ae6a513fc746cd0f811da4&api=mtop.taobao.tblive.portal.live.replay.hide.set&v=1.0&preventFallback=true&type=jsonp&dataType=jsonp&callback=mtopjsonp19&data=%7B%22liveId%22%3A528036070520%2C%22hide%22%3A1%7D", {
  "headers": {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "script",
    "sec-fetch-mode": "no-cors",
    "sec-fetch-site": "same-site"
  },
  "referrer": "https://liveplatform.taobao.com/restful/index/live/list",
  "referrerPolicy": "no-referrer-when-downgrade",
  "body": null,
  "method": "GET",
  "mode": "cors",
  "credentials": "include"
});