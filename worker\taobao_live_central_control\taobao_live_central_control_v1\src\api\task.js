import express from 'express';
import taskManager from '../task-manager.js';
import database from '../database.js';
import { 
    createApiResponse, 
    validateRequiredFields, 
    withErrorHandling,
    getTaskStatusText,
    getTaskTypeText 
} from '../utils/common-utils.js';

const router = express.Router();

/**
 * 创建手卡提取任务
 */
router.post('/hand-card/create', withErrorHandling(async (req, res) => {
    const { liveId, productIds, taskParams } = req.body;

    // 验证必填字段
    const missing = validateRequiredFields(req.body, ['liveId', 'productIds']);
    if (missing.length > 0) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            `缺少必填参数：${missing.join(', ')}`
        ));
    }

    if (!Array.isArray(productIds) || productIds.length === 0) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            '产品ID列表不能为空'
        ));
    }

    // 获取直播信息
    const livePlan = await database.get(
        'SELECT anchor_name, live_title FROM live_plans WHERE live_id = ?',
        [liveId]
    );
    
    if (!livePlan) {
        return res.status(404).json(createApiResponse(
            false, 
            null, 
            '', 
            '未找到直播计划'
        ));
    }

    // 检查主播cookie
    const anchor = await database.get(
        'SELECT anchor_cookie FROM anchors WHERE anchor_name = ?',
        [livePlan.anchor_name]
    );
    
    if (!anchor?.anchor_cookie) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            '主播cookie未配置'
        ));
    }



    // 创建任务（h5Token将在任务执行时从主播cookie中动态提取）
    const taskId = await taskManager.createTask({
        taskName: `${livePlan.live_title} - 手卡提取`,
        taskType: 'hand_card',
        anchorName: livePlan.anchor_name,
        liveId: liveId,
        productIds: productIds,
        params: taskParams || {}, // 传递手卡提取参数
        priority: 7
    });

    res.json({
        success: true,
        taskId: taskId,
        message: '手卡提取任务创建成功，将立即开始执行'
    });

}, '创建手卡提取任务失败'));

/**
 * 创建音频生成任务
 */
router.post('/audio/create', withErrorHandling(async (req, res) => {
    const { liveId, productIds, audioParams } = req.body;
    
    // 验证必填字段
    const missing = validateRequiredFields(req.body, ['liveId', 'productIds']);
    if (missing.length > 0) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            `缺少必填参数：${missing.join(', ')}`
        ));
    }

    if (!Array.isArray(productIds) || productIds.length === 0) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            '产品ID列表不能为空'
        ));
    }

    // 获取直播信息
    const livePlan = await database.get(
        'SELECT anchor_name, live_title FROM live_plans WHERE live_id = ?',
        [liveId]
    );
    
    if (!livePlan) {
        return res.status(404).json(createApiResponse(
            false, 
            null, 
            '', 
            '未找到直播计划'
        ));
    }

    // 创建任务
    const taskId = await taskManager.createTask({
        taskName: `${livePlan.live_title} - 音频生成`,
        taskType: 'audio',
        anchorName: livePlan.anchor_name,
        liveId: liveId,
        productIds: productIds,
        params: audioParams || {},
        priority: 6
    });

    res.json({
        success: true,
        taskId: taskId,
        message: '音频生成任务创建成功'
    });

}, '创建音频生成任务失败'));

/**
 * 创建直播任务
 */
router.post('/live/create', withErrorHandling(async (req, res) => {
    const { liveId, liveParams } = req.body;
    
    // 验证必填字段
    const missing = validateRequiredFields(req.body, ['liveId']);
    if (missing.length > 0) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            `缺少必填参数：${missing.join(', ')}`
        ));
    }

    // 获取直播信息
    const livePlan = await database.get(
        'SELECT anchor_name, live_title FROM live_plans WHERE live_id = ?',
        [liveId]
    );
    
    if (!livePlan) {
        return res.status(404).json(createApiResponse(
            false, 
            null, 
            '', 
            '未找到直播计划'
        ));
    }

    // 创建任务
    const taskId = await taskManager.createTask({
        taskName: `${livePlan.live_title} - 直播任务`,
        taskType: 'live',
        anchorName: livePlan.anchor_name,
        liveId: liveId,
        productIds: [], // 直播任务不需要具体产品列表
        params: liveParams || {},
        priority: 8
    });

    res.json({
        success: true,
        taskId: taskId,
        message: '直播任务创建成功'
    });

}, '创建直播任务失败'));

/**
 * 获取任务列表
 */
router.get('/list', withErrorHandling(async (req, res) => {
    const { 
        status, 
        taskType, 
        anchorName, 
        page = 1, 
        limit = 20 
    } = req.query;

    const filters = {};
    if (status) filters.status = status;
    if (taskType) filters.taskType = taskType;
    if (anchorName) filters.anchorName = anchorName;

    const tasks = await taskManager.getTaskList(filters);
    
    // 分页处理
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;
    const paginatedTasks = tasks.slice(offset, offset + limitNum);

    // 为了保持前端兼容性，直接返回扁平结构
    res.json({
        success: true,
        tasks: paginatedTasks,
        pagination: {
            total: tasks.length,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(tasks.length / limitNum)
        }
    });

}, '获取任务列表失败'));

/**
 * 获取任务详情
 */
router.get('/:taskId/details', withErrorHandling(async (req, res) => {
    const { taskId } = req.params;
    
    if (!taskId || isNaN(parseInt(taskId))) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            '无效的任务ID'
        ));
    }
    
    const taskDetails = await taskManager.getTaskDetails(parseInt(taskId));
    
    // 为了保持前端兼容性，直接返回扁平结构
    res.json({
        success: true,
        ...taskDetails
    });

}, '获取任务详情失败'));

/**
 * 取消任务
 */
router.post('/:taskId/cancel', withErrorHandling(async (req, res) => {
    const { taskId } = req.params;

    if (!taskId || isNaN(parseInt(taskId))) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            '无效的任务ID'
        ));
    }

    const success = await taskManager.cancelTask(parseInt(taskId));
    
    if (success) {
        res.json({
            success: true,
            message: '任务已取消'
        });
    } else {
        res.status(400).json({
            success: false,
            error: '任务无法取消'
        });
    }

}, '取消任务失败'));

/**
 * 暂停任务
 */
router.post('/:taskId/pause', withErrorHandling(async (req, res) => {
    const { taskId } = req.params;

    if (!taskId || isNaN(parseInt(taskId))) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            '无效的任务ID'
        ));
    }

    await taskManager.pauseTask(parseInt(taskId));

    res.json({
        success: true,
        message: '任务已暂停'
    });

}, '暂停任务失败'));

/**
 * 恢复任务
 */
router.post('/:taskId/resume', withErrorHandling(async (req, res) => {
    const { taskId } = req.params;

    if (!taskId || isNaN(parseInt(taskId))) {
        return res.status(400).json(createApiResponse(
            false, 
            null, 
            '', 
            '无效的任务ID'
        ));
    }

    await taskManager.resumeTask(parseInt(taskId));

    res.json({
        success: true,
        message: '任务已恢复'
    });

}, '恢复任务失败'));

/**
 * 重试任务
 */
router.post('/:taskId/retry', async (req, res) => {
    try {
        const { taskId } = req.params;
        
        await taskManager.retryTask(parseInt(taskId));
        
        res.json({
            success: true,
            message: '任务已重新启动'
        });

    } catch (error) {
        console.error('重试任务失败:', error);
        res.status(500).json({
            success: false,
            error: '重试任务失败',
            message: error.message
        });
    }
});

/**
 * 获取任务统计信息
 */
router.get('/stats', async (req, res) => {
    try {
        const { anchorName } = req.query;
        
        let sql = `
            SELECT 
                task_type,
                status,
                COUNT(*) as count,
                AVG(progress_percentage) as avg_progress
            FROM async_tasks
        `;
        
        const params = [];
        if (anchorName) {
            sql += ' WHERE anchor_name = ?';
            params.push(anchorName);
        }
        
        sql += ' GROUP BY task_type, status';
        
        const result = await database.all(sql, params);
        const stats = result.results || [];

        // 获取当前执行状态
        const currentStatus = await taskManager.getCurrentStatus();

        res.json({
            success: true,
            stats: stats,
            currentStatus: currentStatus
        });

    } catch (error) {
        console.error('获取任务统计失败:', error);
        res.status(500).json({
            success: false,
            error: '获取任务统计失败',
            message: error.message
        });
    }
});

/**
 * 手动启动待执行的手卡任务
 */
router.post('/hand-card/:taskId/start', async (req, res) => {
    try {
        const { taskId } = req.params;

        // 检查任务是否存在且为待执行状态
        const task = await database.get(
            'SELECT id, task_name, task_type, status, anchor_name FROM async_tasks WHERE id = ?',
            [parseInt(taskId)]
        );

        if (!task) {
            return res.status(404).json({
                success: false,
                error: '任务不存在'
            });
        }

        if (task.task_type !== 'hand_card') {
            return res.status(400).json({
                success: false,
                error: '只能手动启动手卡任务'
            });
        }

        if (task.status !== 'pending') {
            return res.status(400).json({
                success: false,
                error: `任务状态为 ${task.status}，只能启动待执行状态的任务`
            });
        }

        // 手卡任务现在支持并发执行，直接启动即可

        // 手动触发任务处理
        taskManager.processNextTask();

        res.json({
            success: true,
            message: `手卡任务 "${task.task_name}" 已开始执行`,
            taskId: parseInt(taskId)
        });

    } catch (error) {
        console.error('手动启动手卡任务失败:', error);
        res.status(500).json({
            success: false,
            error: '启动任务失败',
            message: error.message
        });
    }
});



/**
 * 获取主播名称列表
 */
router.get('/anchors', async (req, res) => {
    try {
        // 从主播表中获取所有主播名称
        const result = await database.all(`
            SELECT anchor_name 
            FROM anchors 
            WHERE anchor_name IS NOT NULL AND anchor_name != ''
            ORDER BY anchor_name ASC
        `);

        const anchors = result.results || [];
        const anchorNames = anchors.map(anchor => anchor.anchor_name);

        res.json({
            success: true,
            anchors: anchorNames
        });

    } catch (error) {
        console.error('获取主播名称列表失败:', error);
        res.status(500).json({
            success: false,
            error: '获取主播名称列表失败',
            message: error.message
        });
    }
});

/**
 * 删除已完成的任务
 */
router.delete('/cleanup', async (req, res) => {
    try {
        const { olderThanDays = 7 } = req.query;
        
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - parseInt(olderThanDays));
        
        // 删除指定天数前的已完成任务
        const result = await database.run(`
            DELETE FROM async_tasks 
            WHERE status IN ('completed', 'failed', 'cancelled') 
            AND completed_at < ?
        `, [cutoffDate.toISOString()]);

        res.json({
            success: true,
            deletedCount: result.changes || 0,
            message: `已清理 ${result.changes || 0} 个历史任务`
        });

    } catch (error) {
        console.error('清理任务失败:', error);
        res.status(500).json({
            success: false,
            error: '清理任务失败',
            message: error.message
        });
    }
});

export default router;
