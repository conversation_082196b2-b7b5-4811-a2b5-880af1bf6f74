import express from 'express';
import LiveCardExtractor from '../live-card-extract.js';
import { database } from '../database.js';
import { extractAndValidateH5Token } from '../utils/cookie-utils.js';
import { getAnchorByLiveId } from '../utils/anchor-utils.js';

const router = express.Router();

// 批量提取手卡（后端并发）
router.post('/extract', async (req, res) => {
    const { liveId, itemIds, extractParams } = req.body;
    
    if (!liveId || !Array.isArray(itemIds) || itemIds.length === 0) {
        return res.status(400).json({ success: false, error: '参数错误' });
    }

    // 获取主播信息
    const anchor = await getAnchorByLiveId(liveId);
    if (!anchor) {
        return res.status(404).json({ success: false, error: '未找到直播计划或主播信息' });
    }

    // 提取h5Token
    const h5Token = extractAndValidateH5Token(anchor.anchor_cookie, null);
    if (!h5Token) {
        return res.status(400).json({ success: false, error: '主播cookie无效，缺少h5_tk' });
    }

    const extractor = new LiveCardExtractor();
    // 并发批量提取
    const results = await Promise.all(itemIds.map(async (itemId) => {
        try {
            const card = await extractor.extractCard(liveId, itemId, h5Token, anchor.anchor_cookie, anchor.anchor_name, extractParams);
            if (card && card.script) {
                // 保存到数据库（如果开启了违禁词过滤，这里保存的已经是过滤后的内容）
                await database.run(
                    'UPDATE live_products SET script_content = ?, updated_at = datetime(\'now\', \'+8 hours\') WHERE live_id = ? AND product_id = ?',
                    [card.script, liveId, itemId]
                );

                // 返回结果，包含过滤信息
                const result = {
                    itemId,
                    success: true,
                    productId: itemId  // 添加productId字段，前端需要用到
                };

                // 如果进行了违禁词过滤，添加过滤信息
                if (card.isFiltered !== undefined) {
                    result.isFiltered = card.isFiltered;
                    if (card.filteredWords) {
                        result.filteredWords = card.filteredWords;
                    }
                    if (card.filterError) {
                        result.filterError = card.filterError;
                    }
                }

                return result;
            } else {
                return { itemId, success: false, error: '未获取到手卡内容' };
            }
        } catch (e) {
            return { itemId, success: false, error: e.message };
        }
    }));
    res.json({ success: true, results });
});

// 获取主播信息（用于cookie检测）
router.get('/anchor-info', async (req, res) => {
    try {
        const { liveId } = req.query;
        
        if (!liveId) {
            return res.status(400).json({ success: false, error: '参数错误：需要直播ID' });
        }

        // 查询该直播的主播cookie
        const livePlan = await database.get('SELECT anchor_name FROM live_plans WHERE live_id = ?', [liveId]);
        if (!livePlan) {
            return res.status(404).json({ success: false, error: '未找到直播计划' });
        }

        const anchor = await database.get('SELECT anchor_cookie FROM anchors WHERE anchor_name = ?', [livePlan.anchor_name]);
        if (!anchor || !anchor.anchor_cookie) {
            return res.status(404).json({ success: false, error: '未找到主播cookie' });
        }

        // 提取h5Token
        let h5Token = '';
        try {
            const cookies = anchor.anchor_cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === '_m_h5_tk' && value) {
                    h5Token = value.split('_')[0];
                    break;
                }
            }
        } catch (e) {}

        if (!h5Token) {
            return res.status(400).json({ success: false, error: '主播cookie无效，缺少h5_tk' });
        }

        res.json({
            success: true,
            data: {
                h5Token,
                cookie: anchor.anchor_cookie,
                anchorName: livePlan.anchor_name
            }
        });

    } catch (error) {
        console.error('获取主播信息失败:', error);
        res.status(500).json({ success: false, error: '服务器错误' });
    }
});

// 检测主播cookie有效性
router.post('/check-cookie', async (req, res) => {
    try {
        const { liveId } = req.body;
        
        if (!liveId) {
            return res.status(400).json({ success: false, error: '参数错误：需要直播ID' });
        }

        // 获取主播信息
        const anchor = await getAnchorByLiveId(liveId);
        if (!anchor) {
            return res.json({ valid: false, error: '未找到直播计划或主播信息' });
        }

        // 提取h5Token
        const h5Token = extractAndValidateH5Token(anchor.anchor_cookie, null);
        if (!h5Token) {
            return res.json({ valid: false, error: '主播cookie无效，缺少h5_tk' });
        }

        // 使用LiveCardExtractor的md5方法
        const extractor = new LiveCardExtractor();
        
        // 构建检测请求
        const timestamp = Date.now().toString();
        const data = { version: 1, api: "anchorInfo" };
        const dataStr = JSON.stringify(data);
        const signString = `${h5Token}&${timestamp}&12574478&${dataStr}`;
        const sign = extractor.md5(signString);

        const params = new URLSearchParams({
            jsv: '2.7.4',
            appKey: '12574478',
            t: timestamp,
            sign: sign,
            api: 'mtop.taobao.dreamweb.anchor.homepage.async.get',
            v: '1.0',
            preventFallback: 'true',
            type: 'json',
            dataType: 'json',
            data: dataStr
        });

        const checkUrl = `https://h5api.m.taobao.com/h5/mtop.taobao.dreamweb.anchor.homepage.async.get/1.0/?${params}`;

        const response = await fetch(checkUrl, {
            method: 'GET',
            headers: {
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Referer': `https://liveplatform.taobao.com/restful/index/home/<USER>/edit/v2?liveId=${liveId}`,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
                'Cookie': anchor.anchor_cookie
            }
        });

        if (!response.ok) {
            return res.json({ 
                valid: false, 
                error: `HTTP ${response.status}: ${response.statusText}` 
            });
        }

        const result = await response.json();
        console.log('Cookie检测结果:', result);

        // 检查响应
        if (result.ret && Array.isArray(result.ret) && result.ret.length > 0) {
            const retMsg = result.ret[0];
            if (retMsg.includes('SUCCESS::调用成功')) {
                return res.json({ valid: true });
            } else {
                let errorType = 'UNKNOWN_ERROR';
                if (retMsg.includes('SESSION_EXPIRED') || retMsg.includes('Session过期')) {
                    errorType = 'SESSION_EXPIRED';
                } else if (retMsg.includes('TOKEN_EXOIRED') || retMsg.includes('令牌')) {
                    errorType = 'TOKEN_EXPIRED';
                } else if (retMsg.includes('INVALID_USER') || retMsg.includes('用户无效')) {
                    errorType = 'INVALID_USER';
                }

                return res.json({ 
                    valid: false, 
                    error: retMsg,
                    errorType: errorType
                });
            }
        }

        return res.json({ valid: false, error: '响应格式异常' });

    } catch (error) {
        console.error('Cookie检测失败:', error);
        res.json({ valid: false, error: '检测失败: ' + error.message });
    }
});

// 更新产品手卡内容
router.post('/update-script', async (req, res) => {
    const { liveId, productId, scriptContent } = req.body;

    if (!liveId || !productId || scriptContent === undefined) {
        return res.status(400).json({ success: false, error: '参数错误' });
    }

    try {
        // 更新数据库中的手卡内容
        await database.run(
            'UPDATE live_products SET script_content = ?, updated_at = datetime(\'now\', \'+8 hours\') WHERE live_id = ? AND product_id = ?',
            [scriptContent, liveId, productId]
        );

        res.json({ success: true, message: '手卡内容更新成功' });
    } catch (error) {
        console.error('更新手卡内容失败:', error);
        res.status(500).json({ success: false, error: '更新手卡内容失败' });
    }
});

export default router;