/**
 * 产品列表模态框管理
 * 处理产品列表的显示、统计、操作等功能
 */

class ProductListModal {
    constructor() {
        this.currentLiveId = null;
        this.products = [];
        this.stats = {};
        this.initializeModal();
        this.bindEvents();
    }

    // 初始化模态框HTML结构
    initializeModal() {
        const modalHTML = `
            <!-- 产品列表模态框 -->
            <div id="productListModal" class="fixed z-50 inset-0 overflow-y-auto hidden">
                <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:w-full" style="width: 80%; max-width: none;">
                        
                        <!-- 模态框头部 -->
                        <div class="bg-white px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="product-list-modal-title">
                                    产品列表
                                </h3>
                                <button type="button" onclick="productListModal.hide()" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 第一层：操作区域 -->
                        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                            <div class="flex flex-wrap gap-2">
                                <button id="extractProductsBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
                                    <i class="fas fa-sync mr-2"></i>提取产品列表
                                </button>
                                <button id="extractHandCardsBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                                    <i class="fas fa-hand-paper mr-2"></i>提取手卡
                                </button>
                                <button id="batchExtractHandCardsBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-700 hover:bg-purple-800">
                                    <i class="fas fa-layer-group mr-2"></i>批量提取手卡
                                </button>
                                <button id="createHandCardTaskBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                    <i class="fas fa-tasks mr-2"></i>执行提取手卡任务
                                </button>
                                <button id="prohibitedWordsCheckBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                    <i class="fas fa-shield-alt mr-2"></i>违禁词检测
                                </button>
                                <button id="batchAudioGenerationBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700">
                                    <i class="fas fa-microphone mr-2"></i>执行生成音频任务
                                </button>
                                  <button id="copyProductsBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    <i class="fas fa-copy mr-2"></i>复制
                                </button>
                                <button id="exportProductsBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                                    <i class="fas fa-download mr-2"></i>导出
                                </button>
                                <button id="clearProductsBtn" class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                                    <i class="fas fa-trash mr-2"></i>清空
                                </button>
                            </div>
                        </div>

                        <!-- 第二层：基本信息区域 -->
                        <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="text-sm text-gray-600">
                                        <span class="font-medium">直播ID:</span>
                                        <span id="modalLiveId">--</span>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <span class="font-medium">主播名称:</span>
                                        <span id="modalAnchorName" class="text-orange-600 font-semibold">--</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 第三层：统计数据区域 -->
                        <div class="bg-white px-6 py-4 border-b border-gray-200">
                            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600" id="productCount">--</div>
                                    <div class="text-sm text-gray-500">产品数量</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600" id="avgPrice">--</div>
                                    <div class="text-sm text-gray-500">平均价格</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600" id="avgCommission">--</div>
                                    <div class="text-sm text-gray-500">平均佣金</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-orange-600" id="explainProgress">--</div>
                                    <div class="text-sm text-gray-500">讲解进度</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-500" id="passedCount">--</div>
                                    <div class="text-sm text-gray-500">通过数量</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-red-500" id="failedCount">--</div>
                                    <div class="text-sm text-gray-500">未通过数量</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-yellow-600" id="handCardCount">--</div>
                                    <div class="text-sm text-gray-500">手卡数量</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-xl font-bold text-indigo-600" id="audioCount">--</div>
                                    <div class="text-sm text-gray-500">音频数量</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-xl font-bold text-pink-600" id="pushCount">--</div>
                                    <div class="text-sm text-gray-500">推送数量</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-xl font-bold text-gray-600" id="totalProgress">--</div>
                                    <div class="text-sm text-gray-500">总体进度</div>
                                </div>
                            </div>
                        </div>

                        <!-- 第四层：产品列表区域 -->
                        <div class="bg-white px-6 py-4" style="max-height: 60vh; overflow-y: auto;">
                            <div class="overflow-x-auto">
                                <table class="w-full divide-y divide-gray-200" style="min-width: 1200px;">
                                    <thead class="bg-gray-50 sticky top-0">
                                        <tr>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">选择</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">序号</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">产品ID</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 300px;">产品名称</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">价格</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">佣金</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">佣金率</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">讲解状态</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">手卡状态</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">音频状态</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">推送状态</th>
                                            <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">创建时间</th>
                                        </tr>
                                    </thead>
                                    <tbody id="productListTableBody" class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td colspan="12" class="px-6 py-4 text-center text-sm text-gray-500">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 模态框底部 -->
                        <div class="bg-gray-50 px-6 py-3 flex justify-between items-center">
                            <div class="text-sm text-gray-500">
                                共 <span id="totalProductsCount">0</span> 个产品
                            </div>
                            <button type="button" onclick="productListModal.hide()"
                                class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 手卡参数选择弹窗 -->
            <div id="handCardParamsModal" class="fixed z-50 inset-0 overflow-y-auto hidden">
                <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                        
                        <!-- 弹窗头部 -->
                        <div class="bg-white px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="handCardParamsModalTitle">
                                    手卡提取参数设置
                                </h3>
                                <button type="button" onclick="productListModal.hideHandCardParamsModal()" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 弹窗内容 -->
                        <div class="bg-white px-6 py-4">
                            <!-- 手卡类型选择 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">手卡类型</label>
                                <div class="flex space-x-4">
                                    <label class="flex-1">
                                        <input type="radio" name="handCardType" value="selling_point" class="sr-only" checked>
                                        <div class="handcard-type-btn bg-purple-100 border-2 border-purple-300 text-purple-700 hover:bg-purple-200 cursor-pointer rounded-lg px-4 py-3 text-center text-sm font-medium transition-all duration-200">
                                            商品卖点
                                        </div>
                                    </label>
                                    <label class="flex-1">
                                        <input type="radio" name="handCardType" value="script" class="sr-only">
                                        <div class="handcard-type-btn bg-gray-100 border-2 border-gray-300 text-gray-700 hover:bg-gray-200 cursor-pointer rounded-lg px-4 py-3 text-center text-sm font-medium transition-all duration-200">
                                            讲解脚本
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- 提取类型选择 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">提取类型</label>
                                <div class="flex space-x-4">
                                    <label class="flex-1">
                                        <input type="radio" name="extractType" value="new_content" class="sr-only" checked>
                                        <div class="extract-type-btn bg-blue-100 border-2 border-blue-300 text-blue-700 hover:bg-blue-200 cursor-pointer rounded-lg px-4 py-3 text-center text-sm font-medium transition-all duration-200">
                                            新内容
                                        </div>
                                    </label>
                                    <label class="flex-1">
                                        <input type="radio" name="extractType" value="cached_content" class="sr-only">
                                        <div class="extract-type-btn bg-gray-100 border-2 border-gray-300 text-gray-700 hover:bg-gray-200 cursor-pointer rounded-lg px-4 py-3 text-center text-sm font-medium transition-all duration-200">
                                            缓存内容
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- 手卡处理选项 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">手卡处理</label>
                                <div class="space-y-2">
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" id="filterProhibitedWords" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                        <span class="ml-2 text-sm text-gray-700">过滤违禁词</span>
                                    </label>
                                    <div class="ml-6 text-xs text-gray-500 mt-1">
                                        勾选后将在提取手卡时自动删除违禁词内容
                                    </div>
                                </div>
                            </div>

                            <!-- 自动创建音频任务选择 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">后续任务</label>
                                <div class="space-y-2">
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" id="autoCreateAudioTask" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500" checked>
                                        <span class="ml-2 text-sm text-gray-700">自动创建音频任务</span>
                                    </label>
                                    <div class="ml-6 text-xs text-gray-500 mt-1">
                                        勾选后将在手卡任务完成后自动创建音频生成任务<br>
                                        默认参数：序号1-420，随机语速，并发20，自动创建GPU，任务完成后自动销毁实例
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 弹窗底部 -->
                        <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                            <button type="button" onclick="productListModal.hideHandCardParamsModal()"
                                class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50">
                                取消
                            </button>
                            <button type="button" id="confirmHandCardParams"
                                class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700">
                                确定
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 手卡内容显示弹窗 -->
            <div id="handCardContentModal" class="fixed z-50 inset-0 overflow-y-auto hidden">
                <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                        
                        <!-- 弹窗头部 -->
                        <div class="bg-white px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="handCardContentModalTitle">
                                    手卡内容
                                </h3>
                                <button type="button" onclick="productListModal.hideHandCardContentModal()" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 弹窗内容 -->
                        <div class="bg-white px-6 py-4" style="max-height: 70vh; overflow-y: auto;">
                            <!-- 产品信息 -->
                            <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-gray-700">产品ID：</span>
                                        <span id="handCardProductId" class="text-gray-900">--</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">产品名称：</span>
                                        <span id="handCardProductName" class="text-gray-900">--</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 手卡内容 -->
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <h4 class="text-md font-medium text-gray-900">手卡内容</h4>
                                    <button type="button" id="copyHandCardContent" 
                                        class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200">
                                        <i class="fas fa-copy mr-2"></i>复制内容
                                    </button>
                                </div>
                                <div id="handCardContentText" class="p-4 bg-gray-50 rounded-lg border min-h-[200px] whitespace-pre-wrap text-sm text-gray-800 leading-relaxed">
                                    加载中...
                                </div>
                            </div>

                            <!-- 字数统计 -->
                            <div class="mt-4 text-right text-sm text-gray-500">
                                字数统计：<span id="handCardWordCount">0</span> 字
                            </div>
                        </div>

                        <!-- 弹窗底部 -->
                        <div class="bg-gray-50 px-6 py-4 flex justify-end">
                            <button type="button" onclick="productListModal.hideHandCardContentModal()"
                                class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 移除已存在的模态框
        $('#productListModal').remove();
        $('#handCardParamsModal').remove();
        $('#handCardContentModal').remove();
        
        // 添加新的模态框到页面
        $('body').append(modalHTML);

        // 绑定单选按钮点击事件
        this.bindHandCardModalEvents();
    }

    // 绑定手卡modal的事件
    bindHandCardModalEvents() {
        // 手卡类型按钮点击事件
        $(document).on('click', '.handcard-type-btn', function() {
            // 移除所有选中状态
            $('.handcard-type-btn').removeClass('bg-purple-100 border-purple-300 text-purple-700')
                                   .addClass('bg-gray-100 border-gray-300 text-gray-700');

            // 添加当前选中状态
            $(this).removeClass('bg-gray-100 border-gray-300 text-gray-700')
                   .addClass('bg-purple-100 border-purple-300 text-purple-700');

            // 选中对应的radio
            $(this).prev('input[type="radio"]').prop('checked', true);
        });

        // 提取类型按钮点击事件
        $(document).on('click', '.extract-type-btn', function() {
            // 移除所有选中状态
            $('.extract-type-btn').removeClass('bg-blue-100 border-blue-300 text-blue-700')
                                  .addClass('bg-gray-100 border-gray-300 text-gray-700');

            // 添加当前选中状态
            $(this).removeClass('bg-gray-100 border-gray-300 text-gray-700')
                   .addClass('bg-blue-100 border-blue-300 text-blue-700');

            // 选中对应的radio
            $(this).prev('input[type="radio"]').prop('checked', true);
        });
    }

    // 绑定事件
    bindEvents() {
        // 操作按钮事件
        $('#copyProductsBtn').on('click', () => this.copyProducts());
        $('#exportProductsBtn').on('click', () => this.exportProducts());
        $('#clearProductsBtn').on('click', () => this.clearProducts());
        $('#extractProductsBtn').on('click', () => this.extractProducts());
        $('#extractHandCardsBtn').on('click', () => this.extractHandCards());
        $('#batchExtractHandCardsBtn').on('click', () => this.batchExtractHandCards());
        $('#createHandCardTaskBtn').on('click', () => this.createHandCardTask());
        $('#prohibitedWordsCheckBtn').on('click', () => this.checkProhibitedWords());
        $('#batchAudioGenerationBtn').on('click', () => this.showGpuAudioGeneration());

        // 全选/取消全选
        $(document).on('change', '#selectAllProducts', (e) => {
            const checked = e.target.checked;
            $('.product-checkbox').prop('checked', checked);
        });

        // 单个产品选择
        $(document).on('change', '.product-checkbox', () => {
            const total = $('.product-checkbox').length;
            const checked = $('.product-checkbox:checked').length;
            $('#selectAllProducts').prop('checked', total === checked);
            $('#selectAllProducts').prop('indeterminate', checked > 0 && checked < total);
        });

        // 手卡内容点击事件
        $(document).on('click', '[data-has-script="true"]', (e) => {
            // 查找包含data-product-id属性的最近元素
            const targetElement = $(e.target).closest('[data-product-id]');
            const productId = targetElement.data('product-id');
            if (productId) {
                this.showHandCardContent(productId);
            }
        });
    }

    // 显示产品列表
    async show(liveId, anchorName = '') {
        if (!liveId) {
            layer.msg('直播ID不能为空', { icon: 2 });
            return;
        }

        this.currentLiveId = liveId;

        // 显示模态框
        $('#productListModal').removeClass('hidden');
        
        // 根据是否有主播名称来设置标题
        const title = anchorName ? `${anchorName} - 产品列表 (${liveId})` : `产品列表 - ${liveId}`;
        $('#product-list-modal-title').text(title);

        // 设置基本信息
        $('#modalLiveId').text(liveId);
        $('#modalAnchorName').text(anchorName || '--');

        // 加载数据
        await this.loadData();
    }

    // 隐藏模态框
    hide() {
        $('#productListModal').addClass('hidden');
        this.currentLiveId = null;
        this.products = [];
        this.stats = {};
        
        // 清理基本信息字段
        $('#modalLiveId').text('--');
        $('#modalAnchorName').text('--');
    }

    // 加载数据
    async loadData() {
        try {
            this.showLoadingState();

            // 并行加载产品列表和统计数据
            const [productsResponse, statsResponse] = await Promise.all([
                fetch(`/api/live-products/list?liveId=${this.currentLiveId}`, {
                    headers: this.getApiKeyHeader()
                }),
                fetch(`/api/live-products/stats?liveId=${this.currentLiveId}`, {
                    headers: this.getApiKeyHeader()
                })
            ]);

            // 检查认证
            if (productsResponse.status === 401 || statsResponse.status === 401) {
                this.handleAuthError();
                return;
            }

            const [productsData, statsData] = await Promise.all([
                productsResponse.json(),
                statsResponse.json()
            ]);

            if (!productsResponse.ok) {
                throw new Error(productsData.error || '获取产品列表失败');
            }

            if (!statsResponse.ok) {
                throw new Error(statsData.error || '获取统计数据失败');
            }

            this.products = productsData.products || [];
            this.stats = statsData.stats || {};

            this.updateStatsDisplay();
            this.updateProductTable(); // 放在最后，确保产品数量显示实际表格数据

        } catch (error) {
            console.error('加载数据失败:', error);
            this.showErrorState('加载数据失败: ' + error.message);
            layer.msg('加载数据失败: ' + error.message, { icon: 2 });
        }
    }

    // 更新产品表格
    updateProductTable() {
        const tbody = $('#productListTableBody');
        tbody.empty();

        if (!this.products || this.products.length === 0) {
            this.showEmptyState();
            return;
        }

        // 按序号倒序排列产品
        const sortedProducts = [...this.products].sort((a, b) => {
            const seqA = parseInt(a.product_sequence) || 0;
            const seqB = parseInt(b.product_sequence) || 0;
            return seqB - seqA; // 倒序排列
        });

        // 添加全选行
        tbody.append(`
            <tr class="bg-gray-50">
                <td class="px-3 py-3">
                    <input type="checkbox" id="selectAllProducts" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                </td>
                <td colspan="11" class="px-3 py-3 text-sm font-medium text-gray-700">全选</td>
            </tr>
        `);

        // 添加产品行
        sortedProducts.forEach((product, index) => {
            tbody.append(`
                <tr class="hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}">
                    <td class="px-3 py-4">
                        <input type="checkbox" class="product-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                               value="${product.product_id || product.id}">
                    </td>
                    <td class="px-3 py-4 text-sm text-gray-900 font-medium">${product.product_sequence || '--'}</td>
                    <td class="px-3 py-4 text-sm text-gray-900">${product.product_id || '--'}</td>
                    <td class="px-3 py-4 text-sm text-gray-900" style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                        title="${product.product_name || '--'}">${product.product_name || '--'}</td>
                    <td class="px-3 py-4 text-sm text-gray-900">¥${this.formatNumber(product.amount || 0)}</td>
                    <td class="px-3 py-4 text-sm text-gray-900">¥${this.formatNumber(product.commission_amount || 0)}</td>
                    <td class="px-3 py-4 text-sm text-gray-900">${this.formatPercentage(product.commission_rate || 0)}</td>
                    <td class="px-3 py-4 text-sm">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(product.explanation_status)}">
                            ${product.explanation_status || '未讲解'}
                        </span>
                    </td>
                    <td class="px-3 py-4 text-sm">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(product.script_content ? '有手卡' : '无手卡')} ${product.script_content ? 'cursor-pointer hover:bg-purple-200 transition-colors' : ''}" 
                              ${product.script_content ? `data-product-id="${product.product_id}" data-has-script="true" title="点击查看手卡内容"` : ''}>
                            ${product.script_content ? '<i class="fas fa-eye mr-1"></i>有手卡' : '无手卡'}
                        </span>
                    </td>
                    <td class="px-3 py-4 text-sm">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(product.audio_extraction_status)}">
                            ${product.audio_extraction_status || '未生成'}
                        </span>
                    </td>
                    <td class="px-3 py-4 text-sm">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(product.push_status)}">
                            ${product.push_status || '未推送'}
                        </span>
                    </td>
                    <td class="px-3 py-4 text-sm text-gray-900">${this.formatDateTime(product.created_at)}</td>
                </tr>
            `);
        });

        $('#totalProductsCount').text(sortedProducts.length);

        // 同时更新右上角的产品数量显示，确保与实际表格数据一致
        $('#productCount').text(this.formatNumber(sortedProducts.length));
    }

    // 更新统计显示
    updateStatsDisplay() {
        // 注意：产品数量不在这里更新，而是在updateProductTable中根据实际表格数据更新
        // $('#productCount').text(this.formatNumber(this.stats.productCount || 0));
        $('#avgPrice').text('¥' + this.formatNumber(this.stats.avgPrice || 0));
        $('#avgCommission').text('¥' + this.formatNumber(this.stats.avgCommission || 0));
        $('#explainProgress').text(`${this.stats.explainedCount || 0}/${this.stats.productCount || 0}`);
        $('#passedCount').text(this.formatNumber(this.stats.passedCount || 0));
        $('#failedCount').text(this.formatNumber(this.stats.failedCount || 0));
        $('#handCardCount').text(this.formatNumber(this.stats.handCardCount || 0));
        $('#audioCount').text(this.formatNumber(this.stats.audioCount || 0));
        $('#pushCount').text(this.formatNumber(this.stats.pushCount || 0));

        // 总体进度固定为0
        $('#totalProgress').text('0');
    }

    // 复制产品
    copyProducts() {
        if (!this.products || this.products.length === 0) {
            layer.msg('暂无产品可复制', { icon: 2 });
            return;
        }

        // 按序号升序排列（序号1在最上边）
        const sortedProducts = this.products.sort((a, b) => {
            const seqA = parseInt(a.product_sequence) || 0;
            const seqB = parseInt(b.product_sequence) || 0;
            return seqA - seqB; // 升序排列
        });

        // 只复制产品ID，每行一个
        const copyText = sortedProducts.map(p => p.product_id || '').join('\n');

        navigator.clipboard.writeText(copyText).then(() => {
            layer.msg(`已复制 ${this.products.length} 个产品ID`);
        }).catch(() => {
            layer.msg('复制失败', { icon: 2 });
        });
    }

    // 导出产品
    exportProducts() {
        if (!this.products || this.products.length === 0) {
            layer.msg('没有可导出的产品', { icon: 2 });
            return;
        }

        // 弹出导出选项询问框
        layer.confirm('请选择导出类型：', {
            icon: 3,
            title: '导出选项',
            btn: ['导出未讲解', '导出全部', '取消'],
            btn1: (index) => {
                layer.close(index);
                this.performExport('unexplained');
            },
            btn2: (index) => {
                layer.close(index);
                this.performExport('all');
            },
            btn3: (index) => {
                layer.close(index);
            }
        });
    }

    // 执行导出操作
    performExport(exportType) {
        let productsToExport = [];

        if (exportType === 'all') {
            // 导出全部产品
            productsToExport = this.products;
        } else if (exportType === 'unexplained') {
            // 导出未讲解的产品
            productsToExport = this.products.filter(p => {
                const status = p.explanation_status || '未讲解';
                return status === '未讲解';
            });
        }

        if (productsToExport.length === 0) {
            const message = exportType === 'unexplained' ? '没有未讲解的产品可导出' : '没有可导出的产品';
            layer.msg(message, { icon: 2 });
            return;
        }

        // 使用XLSX库导出
        if (typeof XLSX !== 'undefined') {
            this.createExcelExport(productsToExport, exportType);
        } else {
            layer.msg('导出功能暂时不可用', { icon: 2 });
        }
    }

    // 创建Excel导出
    createExcelExport(productsToExport, exportType = 'all') {
        // 按序号倒序排列
        const sortedProducts = [...productsToExport].sort((a, b) =>
            (b.product_sequence || 0) - (a.product_sequence || 0)
        );

        // 获取主播名称（从第一个产品中获取，如果没有则使用默认值）
        const anchorName = sortedProducts[0]?.anchor_name || '主播';

        // 获取当前日期，格式：YYYY_MM_DD
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const dateStr = `${year}_${month}_${day}`;

        // 创建工作簿
        const wb = XLSX.utils.book_new();

        // 创建批量任务工作表（sheet1）
        this.createBatchTaskSheet(wb, sortedProducts, anchorName);

        // 创建产品列表工作表（sheet2）
        this.createProductListSheet(wb, sortedProducts);

        // 根据导出类型生成文件名
        let fileName;
        if (exportType === 'unexplained') {
            fileName = `批量任务_${anchorName}_未讲解_${dateStr}.xlsx`;
        } else {
            fileName = `批量任务_${anchorName}_${dateStr}.xlsx`;
        }

        XLSX.writeFile(wb, fileName);

        const exportTypeText = exportType === 'unexplained' ? '未讲解' : '全部';
        layer.msg(`已导出 ${exportTypeText} ${sortedProducts.length} 个产品`);
    }

    // 创建批量任务工作表
    createBatchTaskSheet(wb, products, anchorName) {
        // 只导出有手卡内容的产品
        const productsWithScript = products.filter(p => p.script_content && p.script_content.trim());

        const batchTaskData = productsWithScript.map(p => {
            // 处理手卡内容，支持两种类型
            const processedScript = this.processScriptContent(p.script_content);

            return {
                '推理文本': processedScript,
                '音色': `${anchorName}.pt`,
                '语速': 1.2,
                '保存文件名': `${p.product_id}.wav`,
                '音量': 1,
                '产品ID': p.product_id,
                '序号': parseInt(p.product_sequence) || 0
            };
        });

        // 创建工作表
        const ws = XLSX.utils.json_to_sheet(batchTaskData);

        // 设置列宽
        ws['!cols'] = [
            {wch: 50}, // 推理文本
            {wch: 15}, // 音色
            {wch: 8},  // 语速
            {wch: 20}, // 保存文件名
            {wch: 8},  // 音量
            {wch: 15}, // 产品ID
            {wch: 8}   // 序号
        ];

        // 设置数字格式
        const range = XLSX.utils.decode_range(ws['!ref']);
        for (let R = range.s.r + 1; R <= range.e.r; ++R) {
            // 语速列（C列）
            const speedCell = XLSX.utils.encode_cell({r: R, c: 2});
            if (ws[speedCell]) {
                ws[speedCell].t = 'n';
                ws[speedCell].z = '0.0';
            }

            // 音量列（E列）
            const volumeCell = XLSX.utils.encode_cell({r: R, c: 4});
            if (ws[volumeCell]) {
                ws[volumeCell].t = 'n';
                ws[volumeCell].z = '0';
            }

            // 序号列（G列）
            const seqCell = XLSX.utils.encode_cell({r: R, c: 6});
            if (ws[seqCell]) {
                ws[seqCell].t = 'n';
                ws[seqCell].z = '0';
            }
        }

        XLSX.utils.book_append_sheet(wb, ws, '批量任务');
    }

    // 创建产品列表工作表
    createProductListSheet(wb, products) {
        const productListData = products.map(p => ({
            '商品序号': parseInt(p.product_sequence) || 0,
            '商品ID': p.product_id || '',
            '链接': p.product_id ? `https://item.taobao.com/item.htm?id=${p.product_id}` : '',
            '商品名称': p.product_name || '',
            '商品副标题': p.product_subtitle || '',
            '商品价格': parseFloat(p.amount) || 0,
            '佣金比例': parseFloat(p.commission_rate) || 0,
            '佣金类型': p.product_type || '',
            '商品类目': p.product_category || ''
        }));

        // 创建工作表
        const ws = XLSX.utils.json_to_sheet(productListData);

        // 设置列宽
        ws['!cols'] = [
            {wch: 10}, // 商品序号
            {wch: 15}, // 商品ID
            {wch: 50}, // 链接
            {wch: 50}, // 商品名称
            {wch: 30}, // 商品副标题
            {wch: 12}, // 商品价格
            {wch: 12}, // 佣金比例
            {wch: 12}, // 佣金类型
            {wch: 20}  // 商品类目
        ];

        // 设置数字格式
        const range = XLSX.utils.decode_range(ws['!ref']);
        for (let R = range.s.r + 1; R <= range.e.r; ++R) {
            // 商品序号列（A列）
            const seqCell = XLSX.utils.encode_cell({r: R, c: 0});
            if (ws[seqCell]) {
                ws[seqCell].t = 'n';
                ws[seqCell].z = '0';
            }

            // 商品价格列（F列）
            const priceCell = XLSX.utils.encode_cell({r: R, c: 5});
            if (ws[priceCell]) {
                ws[priceCell].t = 'n';
                ws[priceCell].z = '0.00';
            }

            // 佣金比例列（G列）
            const commissionCell = XLSX.utils.encode_cell({r: R, c: 6});
            if (ws[commissionCell]) {
                ws[commissionCell].t = 'n';
                ws[commissionCell].z = '0.00';
            }
        }

        XLSX.utils.book_append_sheet(wb, ws, '产品列表');
    }

    // 清空产品
    clearProducts() {
        layer.confirm('确定要清空所有产品吗？此操作不可恢复！', {
            icon: 3,
            title: '确认清空'
        }, (index) => {
            // 立即关闭确认对话框
            layer.close(index);

            // 显示加载提示
            const loadingIndex = layer.load(1, { shade: [0.1, '#fff'] });

            // 执行清空操作
            this.performClearProducts(loadingIndex);
        });
    }

    // 执行清空产品操作
    async performClearProducts(loadingIndex) {
        try {
            const response = await fetch(`/api/live-products/clear`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getApiKeyHeader()
                },
                body: JSON.stringify({
                    liveId: this.currentLiveId
                })
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || '清空失败');
            }

            layer.msg('产品已清空');
            this.loadData(); // 重新加载数据
        } catch (error) {
            console.error('清空产品失败:', error);
            layer.msg('清空失败: ' + error.message, { icon: 2 });
        } finally {
            // 确保关闭加载提示
            layer.close(loadingIndex);
        }
    }

    // 提取产品列表
    async extractProducts() {
        if (!this.currentLiveId) {
            layer.msg('直播ID不能为空', { icon: 2 });
            return;
        }

       

            // 显示加载状态
            const loadingIndex = layer.msg('正在提取产品...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            try {
                const response = await fetch('/api/live-products/sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...this.getApiKeyHeader()
                    },
                    body: JSON.stringify({
                        liveId: this.currentLiveId
                    })
                });

                const result = await response.json();
                layer.close(loadingIndex);

                if (!response.ok) {
                    throw new Error(result.error || '提取产品失败');
                }

                if (result.success) {
                    const { inserted, updated, deleted, total } = result.data;
                    let message = `产品提取完成！共处理 ${total} 个产品，新增 ${inserted} 个，更新 ${updated} 个`;
                    if (deleted > 0) {
                        message += `，删除 ${deleted} 个`;
                    }
                    layer.msg(message, {
                        icon: 1,
                        time: 5000
                    });

                    // 刷新产品列表
                    await this.loadData();
                } else {
                    layer.msg('产品提取失败: ' + result.error, { icon: 2 });
                }

            } catch (error) {
                layer.close(loadingIndex);
                console.error('提取产品失败:', error);
                layer.msg('提取产品失败: ' + error.message, { icon: 2 });
            }
    }

    // 提取手卡
    async extractHandCards() {
        const selectedIds = this.getSelectedProductIds();
        if (!this.currentLiveId || selectedIds.length === 0) {
            layer.msg('请先勾选要提取手卡的产品', { icon: 0 });
            return;
        }

        // 显示参数选择弹窗
        this.showHandCardParamsModal('提取手卡参数设置', async (params) => {
            await this._doExtractHandCards(selectedIds, params);
        });
    }

    // 执行手卡提取
    async _doExtractHandCards(selectedIds, params) {
        // 第一步：检测cookie有效性
        const cookieCheckIndex = layer.msg('正在检测主播cookie有效性...', {
            icon: 16,
            shade: 0.3,
            time: 0
        });

        try {
            const cookieCheck = await this.checkAnchorCookieValidity(this.currentLiveId);
            layer.close(cookieCheckIndex);

            if (!cookieCheck.valid) {
                // Cookie无效，显示详细错误信息
                layer.msg(`Cookie检测失败: ${cookieCheck.error}`, {icon: 2});
                return; // 停止执行
            }

            console.log(`✅ Cookie检测通过，开始提取手卡`);
        } catch (error) {
            layer.close(cookieCheckIndex);
            console.error('Cookie检测异常:', error);
            layer.msg(`Cookie检测失败: ${error.message}`, {icon: 2});
            return; // 停止执行
        }

        const loadingIndex = layer.msg('正在提取手卡...', { icon: 16, shade: 0.3, time: 0 });
        try {
            const response = await fetch('/api/live-card/extract', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getApiKeyHeader()
                },
                body: JSON.stringify({ 
                    liveId: this.currentLiveId, 
                    itemIds: selectedIds,
                    extractParams: params
                })
            });
            const result = await response.json();
            layer.close(loadingIndex);
            
            if (!response.ok) {
                layer.msg('手卡提取失败: ' + (result.error || `HTTP ${response.status}`), { icon: 2 });
                return;
            }
            
            if (result.success && result.results) {
                const successCount = result.results.filter(r => r.success).length;
                const failCount = result.results.length - successCount;

                // 收集失败的错误信息
                const failedResults = result.results.filter(r => !r.success);

                // 如果勾选了过滤违禁词，显示相应的完成消息
                if (params.filterProhibitedWords && successCount > 0) {
                    if (failCount === 0) {
                        layer.msg(`手卡提取和违禁词过滤完成，成功${successCount}个`, { icon: 1 });
                    } else {
                        const errorDetails = this.summarizeErrors(failedResults);
                        layer.msg(`手卡提取完成，违禁词过滤完成，失败产品错误: ${errorDetails}`, { icon: 3 });
                    }
                    this.loadData();
                    return;
                }

                if (failCount === 0) {
                    // 全部成功
                    layer.msg(`手卡提取完成，成功${successCount}个`, { icon: 1 });
                } else if (successCount === 0) {
                    // 全部失败，显示详细错误
                    const errorDetails = this.summarizeErrors(failedResults);
                    layer.msg(`手卡提取失败: ${errorDetails}`, { icon: 2 });
                } else {
                    // 部分成功，显示详细信息
                    const errorDetails = this.summarizeErrors(failedResults);
                    layer.msg(`手卡提取完成，失败产品错误: ${errorDetails}`, { icon: 3 });
                }
                this.loadData();
            } else {
                layer.msg('手卡提取失败: ' + (result.error || '服务器返回格式错误'), { icon: 2 });
            }
        } catch (error) {
            layer.close(loadingIndex);
            layer.msg('手卡提取异常: ' + error.message, { icon: 2 });
        }
    }

    // 显示自定义进度Modal
    showHandCardProgressModal(total) {
        // 移除已存在的
        $('#customHandCardProgressModal').remove();
        const modalHtml = `
        <div id="customHandCardProgressModal" style="position:fixed;z-index:99999;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;">
            <div style="background:#fff;border-radius:16px;box-shadow:0 8px 32px rgba(0,0,0,0.18);padding:36px 36px 28px 36px;min-width:340px;max-width:90vw;text-align:center;">
                <div style="font-size:20px;font-weight:600;margin-bottom:18px;color:#333;">批量提取手卡</div>
                <div style="margin-bottom:18px;font-size:15px;color:#666;">批量提取中，请勿关闭页面</div>
                <div style="width:100%;height:18px;background:#f3f4f6;border-radius:9px;overflow:hidden;margin-bottom:12px;">
                    <div id="customHandCardProgressBar" style="height:100%;width:0%;background:linear-gradient(90deg,#7c3aed,#a78bfa);transition:width 0.2s;"></div>
                </div>
                <div id="customHandCardProgressText" style="font-size:15px;color:#333;">0/${total}，成功0，失败0</div>
            </div>
        </div>`;
        $('body').append(modalHtml);
    }
    // 更新自定义进度Modal
    updateHandCardProgressModal(completed, total, success, fail) {
        const percent = total === 0 ? 0 : Math.round((completed / total) * 100);
        $('#customHandCardProgressBar').css('width', percent + '%');
        $('#customHandCardProgressText').html(`${completed}/${total}，成功${success}，失败${fail}`);
    }
    // 关闭自定义进度Modal
    closeHandCardProgressModal() {
        $('#customHandCardProgressModal').remove();
    }

    // 线程池批量提取手卡（失败自动重试，直到全部成功或超出最大重试次数）
    async batchExtractHandCards() {
        // 获取当前场次所有产品ID，无需勾选
        const allIds = (this.products || []).map(p => p.product_id || p.id).filter(Boolean);
        if (!this.currentLiveId || allIds.length === 0) {
            layer.msg('当前场次没有可提取的产品', { icon: 0 });
            return;
        }
        
        // 显示参数选择弹窗
        this.showHandCardParamsModal('批量提取手卡参数设置', (params) => {
            // 弹出确认框
            layer.confirm(`确定要批量提取该场次的全部 ${allIds.length} 个产品的手卡吗？`, {
                icon: 3,
                title: '批量提取手卡确认',
                btn: ['确定', '取消']
            }, (index) => {
                // 关闭确认框
                layer.close(index);
                // 执行批量提取
                this._doBatchExtractHandCards(allIds, params);
            });
        });
    }

    // 高效线程池实现 - 混合批量+单任务处理，效率最大化
    async _doBatchExtractHandCards(allIds, params) {
        // 第一步：检测cookie有效性
        const cookieCheckIndex = layer.msg('正在检测主播cookie有效性...', {
            icon: 16,
            shade: 0.3,
            time: 0
        });

        try {
            const cookieCheck = await this.checkAnchorCookieValidity(this.currentLiveId);
            layer.close(cookieCheckIndex);

            if (!cookieCheck.valid) {
                // Cookie无效，显示详细错误信息
                layer.msg(`主播cookie已失效: ${cookieCheck.error}`, { icon: 2 });
                return; // 停止执行
            }

            console.log(`✅ Cookie检测通过，开始批量提取`);
        } catch (error) {
            layer.close(cookieCheckIndex);
            console.error('Cookie检测异常:', error);
            layer.msg(`Cookie检测失败: ${error.message}`, { icon: 2 });
            return; // 停止执行
        }

        const POOL_SIZE = 10;           // 线程池大小：减少到10个避免服务器压力
        const BATCH_SIZE = 5;           // 每个线程批量处理5个手卡，平衡效率和灵活性
        const MAX_RETRY = 3;            // 最大重试次数
        const PROGRESS_UPDATE_INTERVAL = 100; // 进度更新间隔(ms)

        let success = 0;
        let fail = 0;
        const total = allIds.length;
        const results = [];

        // 任务队列管理
        const taskQueue = [...allIds];
        const retryQueue = [];
        const retryCount = new Map();

        // 线程池状态
        let activeThreads = 0;
        let isCompleted = false;
        let lastProgressUpdate = 0;

        // 显示进度Modal
        this.showHandCardProgressModal(total);
        this.updateHandCardProgressModal(success, total, success, fail);

        const startTime = Date.now();

        // 创建HTTP连接复用的fetch函数
        const createOptimizedFetch = () => {
            const controller = new AbortController();
            return {
                fetch: (url, options) => fetch(url, {
                    ...options,
                    signal: controller.signal,
                    keepalive: true  // 启用连接复用
                }),
                abort: () => controller.abort()
            };
        };

        return new Promise((resolve) => {

            // 获取下一批任务（优先重试队列）
            const getNextBatch = () => {
                const batch = [];

                // 优先处理重试队列
                while (retryQueue.length > 0 && batch.length < BATCH_SIZE) {
                    batch.push(retryQueue.shift());
                }

                // 补充新任务
                while (taskQueue.length > 0 && batch.length < BATCH_SIZE) {
                    batch.push(taskQueue.shift());
                }

                return batch;
            };

            // 智能重试延迟（指数退避）
            const getRetryDelay = (retryCount) => {
                return Math.min(1000 * Math.pow(2, retryCount), 5000); // 最大5秒
            };

            // 批量处理手卡
            const processBatch = async (itemIds, httpClient) => {
                try {
                    const response = await httpClient.fetch('/api/live-card/extract', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...this.getApiKeyHeader()
                        },
                        body: JSON.stringify({
                            liveId: this.currentLiveId,
                            itemIds,
                            extractParams: params
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const result = await response.json();

                    if (result.success && result.results) {
                        return result.results.map(item => ({
                            success: item.success,
                            itemId: item.itemId,
                            data: item.success ? item : null,
                            error: item.success ? null : (item.error || '提取失败')
                        }));
                    } else {
                        // 整个批次失败
                        return itemIds.map(itemId => ({
                            success: false,
                            itemId,
                            data: null,
                            error: result.error || '批次处理失败'
                        }));
                    }
                } catch (error) {
                    // 网络错误，整个批次失败
                    return itemIds.map(itemId => ({
                        success: false,
                        itemId,
                        data: null,
                        error: error.message
                    }));
                }
            };

            // 处理批次结果
            const handleBatchResults = (batchResults) => {
                for (const result of batchResults) {
                    if (result.success) {
                        success++;
                        results.push({
                            itemId: result.itemId,
                            success: true,
                            data: result.data
                        });
                    } else {
                        const currentRetry = retryCount.get(result.itemId) || 0;

                        if (currentRetry < MAX_RETRY) {
                            // 加入重试队列，使用指数退避
                            retryCount.set(result.itemId, currentRetry + 1);
                            const delay = getRetryDelay(currentRetry);
                            setTimeout(() => {
                                retryQueue.push(result.itemId);
                            }, delay);
                        } else {
                            // 超过重试次数，标记为失败
                            fail++;
                            results.push({
                                itemId: result.itemId,
                                success: false,
                                error: result.error || '提取失败'
                            });
                        }
                    }
                }

                // 节流更新进度（避免频繁DOM操作）
                const now = Date.now();
                if (now - lastProgressUpdate > PROGRESS_UPDATE_INTERVAL) {
                    this.updateHandCardProgressModal(success, total, success, fail);
                    lastProgressUpdate = now;
                }
            };

            // 工作线程 - 批量处理模式
            const workerThread = async (threadId) => {

                // 为每个线程创建独立的HTTP客户端
                const httpClient = createOptimizedFetch();

                try {
                    while (!isCompleted) {
                        const batch = getNextBatch();

                        if (batch.length === 0) {
                            // 没有任务了，检查是否真的完成了
                            if (success + fail >= total) {
                                break;
                            }
                            // 等待一下，可能有重试任务
                            await new Promise(resolve => setTimeout(resolve, 200));
                            continue;
                        }

                        const batchResults = await processBatch(batch, httpClient);
                        handleBatchResults(batchResults);

                        // 检查是否完成
                        if (success + fail >= total) {
                            isCompleted = true;
                            break;
                        }

                        // 小延迟避免过于频繁的请求
                        await new Promise(resolve => setTimeout(resolve, 50));
                    }
                } catch (error) {
                    console.error(`❌ 线程${threadId} 异常:`, error);
                } finally {
                    httpClient.abort(); // 清理HTTP连接
                }

                activeThreads--;

                // 检查是否需要结束
                if (activeThreads === 0) {
                    // 最后一次更新进度
                    this.updateHandCardProgressModal(success, total, success, fail);

                    const endTime = Date.now();
                    const duration = ((endTime - startTime) / 1000).toFixed(2);
                    const successRate = ((success / total) * 100).toFixed(1);
                    const avgSpeed = (total / (endTime - startTime) * 1000).toFixed(1);

                    setTimeout(() => this.closeHandCardProgressModal(), 500);

                    // 如果勾选了过滤违禁词，显示相应的完成消息
                    if (params.filterProhibitedWords && success > 0) {
                        if (fail === 0) {
                            layer.msg(`批量提取和违禁词过滤完成！成功${success}个，耗时${duration}秒，速度${avgSpeed}个/秒`, { icon: 1 });
                        } else {
                            const failedResults = results.filter(r => !r.success);
                            const errorDetails = this.summarizeErrors(failedResults);
                            layer.msg(`批量提取完成，违禁词过滤完成，失败产品错误: ${errorDetails}`, { icon: 3 });
                        }
                        this.loadData();
                        resolve(results);
                        return;
                    }

                    // 显示详细的结果信息
                    if (fail === 0) {
                        // 全部成功
                        layer.msg(`批量提取完成！成功${success}个，耗时${duration}秒，速度${avgSpeed}个/秒`, { icon: 1 });
                    } else if (success === 0) {
                        // 全部失败，显示详细错误
                        const failedResults = results.filter(r => !r.success);
                        const errorDetails = this.summarizeErrors(failedResults);
                        layer.msg(`批量提取失败: ${errorDetails}`, { icon: 2 });
                    } else {
                        // 部分成功，显示详细信息
                        const failedResults = results.filter(r => !r.success);
                        const errorDetails = this.summarizeErrors(failedResults);
                        layer.msg(`批量提取完成，失败产品错误: ${errorDetails}`, { icon: 3 });
                    }

                    this.loadData();
                    resolve(results);
                }
            };

            // 启动线程池
            activeThreads = POOL_SIZE;
            for (let i = 1; i <= POOL_SIZE; i++) {
                workerThread(i);
            }
        });
    }

    // 获取选中的产品ID
    getSelectedProductIds() {
        return $('.product-checkbox:checked').map(function() {
            return this.value;
        }).get();
    }

    // 汇总错误信息
    summarizeErrors(failedResults) {
        if (!failedResults || failedResults.length === 0) {
            return '无错误信息';
        }

        // 统计错误类型和数量
        const errorStats = {};
        const detailedErrors = [];

        failedResults.forEach(result => {
            const error = result.error || '未知错误';
            
            // 统计相同错误的数量
            if (errorStats[error]) {
                errorStats[error].count++;
                errorStats[error].itemIds.push(result.itemId);
            } else {
                errorStats[error] = {
                    count: 1,
                    itemIds: [result.itemId]
                };
            }
        });

        // 格式化错误信息
        const errorLines = [];
        for (const [errorMsg, info] of Object.entries(errorStats)) {
            let formattedError = errorMsg;
            
            // 特殊处理常见错误
            if (errorMsg.includes('Session过期') || errorMsg.includes('SESSION_EXPIRED')) {
                formattedError = '🔐 登录会话已过期，请重新登录主播账号';
            } else if (errorMsg.includes('TOKEN_EXOIRED') || errorMsg.includes('令牌过期')) {
                formattedError = '🔑 访问令牌已过期，请刷新页面重试';
            } else if (errorMsg.includes('未获取到手卡内容')) {
                formattedError = '📝 该产品暂无手卡内容';
            } else if (errorMsg.includes('网络') || errorMsg.includes('timeout')) {
                formattedError = '🌐 网络连接失败，请检查网络后重试';
            }

            const itemIdsStr = info.itemIds.length > 3 
                ? `${info.itemIds.slice(0, 3).join(', ')} 等${info.itemIds.length}个`
                : info.itemIds.join(', ');

            if (info.count === 1) {
                errorLines.push(`• ${formattedError}<br>&nbsp;&nbsp;产品ID: ${itemIdsStr}`);
            } else {
                errorLines.push(`• ${formattedError} (${info.count}个产品)<br>&nbsp;&nbsp;产品ID: ${itemIdsStr}`);
            }
        }

        return errorLines.join('<br><br>');
    }

    // 显示加载状态
    showLoadingState() {
        $('#productListTableBody').html(`
            <tr>
                <td colspan="12" class="px-6 py-4 text-center text-sm text-gray-500">
                    <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                </td>
            </tr>
        `);
    }

    // 显示空状态
    showEmptyState() {
        $('#productListTableBody').html(`
            <tr>
                <td colspan="12" class="px-6 py-12 text-center">
                    <div class="text-gray-500">
                        <i class="fas fa-box-open text-4xl mb-4 text-gray-300"></i>
                        <p class="text-lg font-medium">暂无产品数据</p>
                        <p class="text-sm mt-2">请先使用"提取产品"功能获取产品列表</p>
                    </div>
                </td>
            </tr>
        `);
        
        // 清空统计数据
        $('#productCount').text('0');
        $('#avgPrice').text('¥0');
        $('#avgCommission').text('¥0');
        $('#explainProgress').text('0/0');
        $('#passedCount').text('0');
        $('#failedCount').text('0');
        $('#handCardCount').text('0');
        $('#audioCount').text('0');
        $('#pushCount').text('0');
        $('#totalProgress').text('0%');
        $('#totalProductsCount').text('0');
    }

    // 显示错误状态
    showErrorState(message) {
        $('#productListTableBody').html(`
            <tr>
                <td colspan="12" class="px-6 py-4 text-center text-sm text-red-500">
                    <i class="fas fa-exclamation-triangle mr-2"></i>${message}
                </td>
            </tr>
        `);
    }

    // 格式化数字
    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return parseFloat(num).toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 });
    }

    // 格式化百分比
    formatPercentage(num) {
        if (num === null || num === undefined) return '0%';
        return (parseFloat(num)).toFixed(2) + '%';
    }

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '--';
        try {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return dateTimeStr;
        }
    }

    // 获取状态样式类
    getStatusClass(status) {
        const statusMap = {
            // 讲解状态
            '讲解通过': 'bg-green-100 text-green-800',
            '讲解未通过': 'bg-red-100 text-red-800', 
            '已讲解': 'bg-blue-100 text-blue-800',
            '未讲解': 'bg-gray-100 text-gray-800',
            
            // 手卡状态
            '有手卡': 'bg-purple-100 text-purple-800',
            '无手卡': 'bg-gray-100 text-gray-800',
            
            // 音频状态
            '已生成': 'bg-indigo-100 text-indigo-800',
            '未生成': 'bg-gray-100 text-gray-800',
            '生成中': 'bg-yellow-100 text-yellow-800',
            
            // 推送状态
            '已推送': 'bg-green-100 text-green-800',
            '未推送': 'bg-yellow-100 text-yellow-800',
            '推送中': 'bg-blue-100 text-blue-800',
            '推送失败': 'bg-red-100 text-red-800'
        };
        
        return statusMap[status] || 'bg-gray-100 text-gray-800';
    }

    // 处理认证错误
    handleAuthError() {
        if (typeof showApiKeyModal === 'function') {
            showApiKeyModal();
        } else {
            layer.msg('认证失败，请重新登录', { icon: 2 });
        }
    }

    // 获取API密钥头部
    getApiKeyHeader() {
        if (typeof addApiKeyHeader === 'function') {
            return addApiKeyHeader();
        }
        
        const apiKey = this.getCookie('api_key');
        return apiKey ? { 'X-API-Key': apiKey } : {};
    }

    // 创建手卡提取任务
    async createHandCardTask() {
        if (!this.currentLiveId) {
            layer.msg('请先选择直播场次', { icon: 0 });
            return;
        }

        // 获取当前场次所有产品ID
        const allIds = (this.products || []).map(p => p.product_id || p.id).filter(Boolean);
        if (allIds.length === 0) {
            layer.msg('当前场次没有可提取的产品', { icon: 0 });
            return;
        }

        // 显示参数选择弹窗
        this.showHandCardParamsModal('执行手卡任务参数设置', async (params) => {
            await this._doCreateHandCardTask(allIds, params);
        });
    }

    // 执行创建手卡任务
    async _doCreateHandCardTask(allIds, params) {
        // 第一步：检测cookie有效性
        const cookieCheckIndex = layer.msg('正在检测主播cookie有效性...', {
            icon: 16,
            shade: 0.3,
            time: 0
        });

        try {
            const cookieCheck = await this.checkAnchorCookieValidity(this.currentLiveId);
            layer.close(cookieCheckIndex);

            if (!cookieCheck.valid) {
                // Cookie无效，显示详细错误信息
                layer.msg(`主播cookie已失效: ${cookieCheck.error}`, { icon: 2 });
                return; // 停止执行
            }

            console.log(`✅ Cookie检测通过，准备创建任务`);
        } catch (error) {
            layer.close(cookieCheckIndex);
            console.error('Cookie检测异常:', error);
            layer.msg(`Cookie检测失败: ${error.message}`, { icon: 2 });
            return; // 停止执行
        }

        // 显示确认对话框
        layer.confirm(`将处理 ${allIds.length} 个产品，任务将在后台异步执行。`, {
            icon: 3,
            title: '确认创建任务',
            btn: ['确定', '取消']
        }, async (index) => {
            layer.close(index);

            // 显示加载状态
            const loadingIndex = layer.msg('正在创建手卡提取任务...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            try {
                // 创建手卡提取任务（h5Token将在后端动态获取）
                const response = await fetch('/api/task/hand-card/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...this.getApiKeyHeader()
                    },
                    body: JSON.stringify({
                        liveId: this.currentLiveId,
                        productIds: allIds,
                        taskParams: params
                        // h5Token 将在后端从主播cookie中动态提取
                    })
                });

                layer.close(loadingIndex);

                const result = await response.json();

                console.log('任务创建响应:', result);

                if (result.success) {
                    layer.msg(`手卡提取任务创建成功！<br>任务ID: ${result.taskId}<br>产品数量: ${allIds.length}`, {
                        icon: 1,
                        time: 5000
                    });

                    // 询问是否打开任务管理页面
                    setTimeout(() => {
                        layer.confirm('任务已创建，是否打开任务管理页面查看进度？', {
                            btn: ['打开任务管理', '稍后查看']
                        }, function(index) {
                            window.open('/task-manager.html', '_blank');
                            layer.close(index);
                        });
                    }, 1000);

                    // 刷新产品列表
                    setTimeout(() => this.loadData(), 2000);

                } else {
                    layer.msg('创建手卡提取任务失败: ' + (result.error || '未知错误'), { icon: 2 });
                }

            } catch (error) {
                layer.close(loadingIndex);
                console.error('创建手卡提取任务失败:', error);
                layer.msg('创建手卡提取任务失败', { icon: 2 });
            }
        });
    }

    // 获取Cookie
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }



    // 检测主播cookie有效性
    async checkAnchorCookieValidity(liveId) {
        try {
            console.log(`🔐 开始检测主播cookie有效性 - liveId: ${liveId}`);

            // 直接使用后端检测API（更可靠）
            return await this.checkCookieViaBackend(liveId);

        } catch (error) {
            console.error('Cookie检测失败:', error);
            return { valid: false, error: error.message };
        }
    }

    // 通过后端检测cookie有效性
    async checkCookieViaBackend(liveId) {
        try {
            console.log(`🔧 使用后端API检测cookie有效性 - liveId: ${liveId}`);
            
            const response = await fetch('/api/live-card/check-cookie', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getApiKeyHeader()
                },
                body: JSON.stringify({ liveId })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log(`🎯 后端检测结果:`, result);

            // 确保返回格式一致
            if (result.valid === true) {
                console.log(`✅ 后端检测：Cookie有效`);
                return { valid: true };
            } else {
                console.log(`❌ 后端检测：Cookie无效 - ${result.error}`);
                return { 
                    valid: false, 
                    error: result.error || '未知错误',
                    errorType: result.errorType || this.parseCookieError(result.error)
                };
            }
        } catch (error) {
            console.error('后端检测异常:', error);
            return { valid: false, error: '后端检测失败: ' + error.message };
        }
    }

    // 解析cookie错误类型
    parseCookieError(errorMsg) {
        if (errorMsg.includes('SESSION_EXPIRED') || errorMsg.includes('Session过期')) {
            return 'SESSION_EXPIRED';
        } else if (errorMsg.includes('TOKEN_EXOIRED') || errorMsg.includes('令牌过期')) {
            return 'TOKEN_EXPIRED';
        } else if (errorMsg.includes('INVALID_USER') || errorMsg.includes('用户无效')) {
            return 'INVALID_USER';
        } else {
            return 'UNKNOWN_ERROR';
        }
    }

    // 格式化cookie错误信息
    formatCookieError(errorMsg, errorType) {
        switch (errorType) {
            case 'SESSION_EXPIRED':
                return '🔐 登录会话已过期<br>&nbsp;&nbsp;解决方案：重新登录主播账号';
            case 'TOKEN_EXPIRED':
                return '🔑 访问令牌已过期<br>&nbsp;&nbsp;解决方案：刷新页面或重新登录';
            case 'INVALID_USER':
                return '👤 用户身份无效<br>&nbsp;&nbsp;解决方案：检查主播账号权限';
            default:
                return `❌ ${errorMsg || '未知错误'}<br>&nbsp;&nbsp;解决方案：检查网络连接或联系技术支持`;
        }
    }

    // 显示GPU音频生成模态框
    showGpuAudioGeneration() {
        if (!this.currentLiveId) {
            layer.msg('请先选择直播场次', { icon: 0 });
            return;
        }

        // 检查是否已加载GPU管理器
        if (typeof showGpuAccountsModal === 'function') {
            showGpuAccountsModal(this.currentLiveId);
        } else {
            layer.msg('GPU音频生成功能未加载，请刷新页面重试', { icon: 2 });
        }
    }

    // 显示手卡参数选择弹窗
    showHandCardParamsModal(title, callback) {
        $('#handCardParamsModalTitle').text(title);

        // 重置表单状态
        $('input[name="handCardType"][value="selling_point"]').prop('checked', true);
        $('input[name="extractType"][value="new_content"]').prop('checked', true);
        $('#autoCreateAudioTask').prop('checked', true);
        $('#filterProhibitedWords').prop('checked', false);

        // 重置按钮样式
        $('.handcard-type-btn').removeClass('bg-purple-100 border-purple-300 text-purple-700')
                               .addClass('bg-gray-100 border-gray-300 text-gray-700');
        $('input[name="handCardType"][value="selling_point"]').next('.handcard-type-btn')
                               .removeClass('bg-gray-100 border-gray-300 text-gray-700')
                               .addClass('bg-purple-100 border-purple-300 text-purple-700');

        $('.extract-type-btn').removeClass('bg-blue-100 border-blue-300 text-blue-700')
                              .addClass('bg-gray-100 border-gray-300 text-gray-700');
        $('input[name="extractType"][value="new_content"]').next('.extract-type-btn')
                              .removeClass('bg-gray-100 border-gray-300 text-gray-700')
                              .addClass('bg-blue-100 border-blue-300 text-blue-700');

        $('#handCardParamsModal').removeClass('hidden');

        // 绑定确定按钮事件
        $('#confirmHandCardParams').off('click').on('click', () => {
            const params = this.getHandCardParams();
            this.hideHandCardParamsModal();
            if (callback) {
                callback(params);
            }
        });
    }

    // 隐藏手卡参数选择弹窗
    hideHandCardParamsModal() {
        $('#handCardParamsModal').addClass('hidden');
    }

    // 获取手卡参数
    getHandCardParams() {
        const handCardType = $('input[name="handCardType"]:checked').val();
        const extractType = $('input[name="extractType"]:checked').val();
        const autoCreateAudioTask = $('#autoCreateAudioTask').is(':checked');
        const filterProhibitedWords = $('#filterProhibitedWords').is(':checked');

        console.log(`🔧 前端参数选择 - 手卡类型: ${handCardType}, 提取类型: ${extractType}, 自动创建音频任务: ${autoCreateAudioTask}, 过滤违禁词: ${filterProhibitedWords}`);

        let type, subType, isRenew, scene;

        // 设置手卡类型参数
        if (handCardType === 'selling_point') {
            type = '1';
            subType = 1;
        } else if (handCardType === 'script') {
            type = '0';
            subType = 4;
        }

        // 设置提取类型参数
        if (extractType === 'new_content') {
            isRenew = true;
            scene = 'renew';
        } else if (extractType === 'cached_content') {
            isRenew = false;
            scene = 'normal';
        }

        const params = {
            type,
            subType,
            isRenew,
            scene,
            autoCreateAudioTask,
            filterProhibitedWords
        };

        console.log(`🔧 前端生成的参数:`, params);

        return params;
    }

    // 处理手卡内容
    // 支持两种类型的手卡处理：
    // 1. 商品卖点类型：移除序号标记和【】中的内容
    // 2. 讲解脚本类型：只提取"### 商品讲解"部分的内容
    processScriptContent(scriptContent) {
        if (!scriptContent) return '';

        // 检查是否为讲解脚本类型（包含### 标题）
        if (scriptContent.includes('### ')) {
            // 第二种类型：讲解脚本，只提取"### 商品讲解"部分
            const sections = scriptContent.split(/###\s*/);

            // 查找"商品讲解"部分
            for (let i = 0; i < sections.length; i++) {
                const section = sections[i].trim();
                if (section.startsWith('商品讲解')) {
                    // 提取商品讲解部分的内容（去掉标题）
                    const content = section.replace(/^商品讲解\s*/, '').trim();
                    return content
                        .replace(/\n+/g, ' ') // 将换行符替换为空格
                        .trim(); // 去掉首尾空格
                }
            }

            // 如果没有找到"商品讲解"部分，返回空字符串
            console.warn('未找到"### 商品讲解"部分，手卡内容可能格式不正确');
            return '';
        } else {
            // 第一种类型：商品卖点，移除序号标记和【】中的内容
            return scriptContent
                .replace(/【[^】]*】/g, '') // 去掉【xx】
                .replace(/^\d+\.\s*/gm, '') // 去掉行首的数字序号（如"1. "）
                .replace(/\n+/g, ' ') // 将换行符替换为空格
                .trim(); // 去掉首尾空格
        }
    }

    // 显示手卡内容弹窗
    showHandCardContent(productId) {
        // 根据product_id字段匹配产品，支持字符串和数字类型
        const product = this.products.find(p => 
            p.product_id === productId || 
            p.product_id == productId || 
            String(p.product_id) === String(productId)
        );
        
        if (!product) {
            layer.msg('未找到该产品信息', { icon: 2 });
            return;
        }
        
        if (!product.script_content || product.script_content.trim() === '') {
            layer.msg('该产品暂无手卡内容', { icon: 2 });
            return;
        }

        // 设置产品信息
        $('#handCardProductId').text(productId);
        $('#handCardProductName').text(product.product_name || '--');
        
        // 设置手卡内容
        const content = product.script_content || '';
        $('#handCardContentText').text(content);
        
        // 计算字数（去除空格和换行符）
        const wordCount = content.replace(/\s/g, '').length;
        $('#handCardWordCount').text(wordCount);
        
        // 绑定复制按钮事件
        $('#copyHandCardContent').off('click').on('click', () => {
            this.copyHandCardContent(content);
        });
        
        // 显示弹窗
        $('#handCardContentModal').removeClass('hidden');
    }

    // 隐藏手卡内容弹窗
    hideHandCardContentModal() {
        $('#handCardContentModal').addClass('hidden');
        
        // 清理内容
        $('#handCardProductId').text('--');
        $('#handCardProductName').text('--');
        $('#handCardContentText').text('');
        $('#handCardWordCount').text('0');
    }

    // 复制手卡内容
    copyHandCardContent(content) {
        if (!content) {
            layer.msg('没有可复制的内容', { icon: 2 });
            return;
        }

        navigator.clipboard.writeText(content).then(() => {
            layer.msg('手卡内容已复制到剪贴板', { icon: 1 });
        }).catch(() => {
            // 兼容性处理：使用旧的方法
            const textArea = document.createElement('textarea');
            textArea.value = content;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                layer.msg('手卡内容已复制到剪贴板', { icon: 1 });
            } catch (err) {
                layer.msg('复制失败，请手动复制', { icon: 2 });
            }
            document.body.removeChild(textArea);
        });
    }

    // 违禁词检测功能
    async checkProhibitedWords() {
        if (!this.currentLiveId) {
            layer.msg('请先选择直播场次', { icon: 0 });
            return;
        }

        // 获取当前场次所有有手卡内容的产品
        const productsWithScript = (this.products || []).filter(p =>
            p.script_content && p.script_content.trim()
        );

        if (productsWithScript.length === 0) {
            layer.msg('当前场次没有产品手卡内容需要检测', { icon: 0 });
            return;
        }

        // 显示确认对话框
        layer.confirm(`将检测 ${productsWithScript.length} 个产品的手卡违禁词，如发现违禁词将自动删除并保存。`, {
            icon: 3,
            title: '确认违禁词检测',
            btn: ['开始检测', '取消']
        }, async (index) => {
            layer.close(index);
            await this._doProhibitedWordsCheck(productsWithScript);
        });
    }

    // 执行违禁词检测
    async _doProhibitedWordsCheck(products) {
        // 禁用违禁词检测按钮
        $('#prohibitedWordsCheckBtn').prop('disabled', true).addClass('opacity-50');

        // 显示进度弹窗
        this.showProhibitedWordsProgressModal(products.length);

        try {
            // 获取违禁词列表
            const prohibitedWords = await this.loadProhibitedWords();
            if (!prohibitedWords || prohibitedWords.length === 0) {
                layer.msg('无法加载违禁词列表', { icon: 2 });
                this.closeProhibitedWordsProgressModal();
                return;
            }

            let processedCount = 0;
            let totalProhibitedWords = 0;
            const foundProhibitedWords = new Set();
            const processedProducts = [];
            const errorProducts = [];

            // 逐个处理产品
            for (const product of products) {
                try {
                    // 更新进度
                    this.updateProhibitedWordsProgress(processedCount + 1, products.length,
                        `正在检测产品: ${product.product_name || product.product_id}`);

                    // 检查手卡内容是否为空
                    if (!product.script_content || !product.script_content.trim()) {
                        processedCount++;
                        continue;
                    }

                   

                    const result = this.detectAndCleanProhibitedWords(product.script_content, prohibitedWords);

                  

                    if (result.foundWords.length > 0) {
                        try {
                            // 发现违禁词，更新数据库
                            await this.updateProductScript(product.product_id, result.cleanedContent);

                            // 更新本地数据
                            const productIndex = this.products.findIndex(p => p.product_id === product.product_id);
                            if (productIndex !== -1) {
                                this.products[productIndex].script_content = result.cleanedContent;
                            }

                            totalProhibitedWords += result.foundWords.length;
                            result.foundWords.forEach(word => foundProhibitedWords.add(word));

                            processedProducts.push({
                                productId: product.product_id,
                                productName: product.product_name,
                                foundWords: result.foundWords
                            });
                        } catch (updateError) {
                            console.error(`更新产品 ${product.product_id} 数据库失败:`, updateError);
                            errorProducts.push({
                                productId: product.product_id,
                                productName: product.product_name,
                                error: updateError.message
                            });
                        }
                    }

                    processedCount++;

                    // 添加小延迟避免界面卡顿
                    await new Promise(resolve => setTimeout(resolve, 50));

                } catch (error) {
                    console.error(`处理产品 ${product.product_id} 时出错:`, error);
                    errorProducts.push({
                        productId: product.product_id,
                        productName: product.product_name,
                        error: error.message
                    });
                    processedCount++;
                }
            }

            // 关闭进度弹窗
            this.closeProhibitedWordsProgressModal();

            // 显示处理结果
            this.showProhibitedWordsResult(processedCount, totalProhibitedWords,
                Array.from(foundProhibitedWords), processedProducts, errorProducts);

            // 刷新产品列表显示
            this.updateProductTable();

        } catch (error) {
            console.error('违禁词检测失败:', error);
            this.closeProhibitedWordsProgressModal();
            layer.msg('违禁词检测失败: ' + error.message, { icon: 2 });
        } finally {
            // 恢复违禁词检测按钮状态
            $('#prohibitedWordsCheckBtn').prop('disabled', false).removeClass('opacity-50');
        }
    }

    // 加载违禁词列表
    async loadProhibitedWords() {
        try {
            const response = await fetch('/src/utils/prohibited_words.json');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('加载违禁词列表失败:', error);
            throw new Error('无法加载违禁词列表');
        }
    }

    // 为指定产品过滤违禁词
    async filterProhibitedWordsForProducts(productIds) {
        if (!productIds || productIds.length === 0) {
            throw new Error('没有指定要过滤的产品');
        }

        // 获取违禁词列表
        const prohibitedWords = await this.loadProhibitedWords();
        if (!prohibitedWords || prohibitedWords.length === 0) {
            console.log('没有违禁词列表，跳过过滤');
            return;
        }

        // 获取指定产品的手卡内容
        const targetProducts = this.products.filter(p => productIds.includes(p.product_id));
        if (targetProducts.length === 0) {
            throw new Error('没有找到指定的产品');
        }

        let processedCount = 0;
        let totalProhibitedWords = 0;
        const foundProhibitedWords = new Set();
        const processedProducts = [];
        const errorProducts = [];

        for (const product of targetProducts) {
            if (!product.script_content || !product.script_content.trim()) {
                continue;
            }

            try {
                // 检测并清理违禁词
                const result = this.detectAndCleanProhibitedWords(product.script_content, prohibitedWords);

                if (result.foundWords.length > 0) {
                    // 发现违禁词，更新数据库
                    await this.updateProductScript(product.product_id, result.cleanedContent);

                    // 更新本地数据
                    const productIndex = this.products.findIndex(p => p.product_id === product.product_id);
                    if (productIndex !== -1) {
                        this.products[productIndex].script_content = result.cleanedContent;
                    }

                    totalProhibitedWords += result.foundWords.length;
                    result.foundWords.forEach(word => foundProhibitedWords.add(word));

                    processedProducts.push({
                        productId: product.product_id,
                        productName: product.product_name,
                        foundWords: result.foundWords
                    });
                }
            } catch (updateError) {
                console.error(`更新产品 ${product.product_id} 数据库失败:`, updateError);
                errorProducts.push({
                    productId: product.product_id,
                    productName: product.product_name,
                    error: updateError.message
                });
            }

            processedCount++;
        }

        console.log(`违禁词过滤完成: 处理${processedCount}个产品，发现${totalProhibitedWords}个违禁词，成功处理${processedProducts.length}个产品`);

        if (processedProducts.length > 0) {
            // 显示过滤结果
            this.showProhibitedWordsResult(
                processedCount,
                totalProhibitedWords,
                Array.from(foundProhibitedWords),
                processedProducts,
                errorProducts
            );
        }
    }

    // 检测并清理违禁词
    detectAndCleanProhibitedWords(content, prohibitedWords) {
        if (!content || !content.trim()) {
            return {
                cleanedContent: content,
                foundWords: []
            };
        }

        let cleanedContent = content;
        const foundWords = [];

        // 按长度倒序排列违禁词，优先处理长词汇，避免短词汇影响长词汇的检测
        const sortedWords = [...prohibitedWords].sort((a, b) => b.length - a.length);

        // 检测并删除违禁词
        sortedWords.forEach(word => {
            if (!word || !word.trim()) return;

            const trimmedWord = word.trim();

            // 检查当前内容中是否包含该违禁词
            if (cleanedContent.includes(trimmedWord)) {
                // 记录发现的违禁词（避免重复）
                if (!foundWords.includes(trimmedWord)) {
                    foundWords.push(trimmedWord);
                }

                // 直接删除违禁词，不替换为任何字符，保持原有格式
                const escapedWord = trimmedWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const regex = new RegExp(escapedWord, 'g');
                cleanedContent = cleanedContent.replace(regex, '');
            }
        });

        return {
            cleanedContent: cleanedContent,
            foundWords: foundWords
        };
    }

    // 更新产品手卡内容到数据库
    async updateProductScript(productId, newContent) {
        try {
            const response = await fetch('/api/live-card/update-script', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getApiKeyHeader()
                },
                body: JSON.stringify({
                    liveId: this.currentLiveId,
                    productId: productId,
                    scriptContent: newContent
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`更新产品 ${productId} 手卡失败:`, error);
            throw error;
        }
    }

    // 显示违禁词检测进度弹窗
    showProhibitedWordsProgressModal(totalCount) {
        const modalHtml = `
            <div id="prohibitedWordsProgressModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 text-center mb-4">违禁词检测进度</h3>

                        <div class="mb-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-2">
                                <span id="prohibitedWordsCurrentStatus">准备开始...</span>
                                <span id="prohibitedWordsProgressText">0/${totalCount}</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="prohibitedWordsProgressBar" class="bg-red-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>

                        <div class="text-center">
                            <div class="inline-flex items-center px-4 py-2 text-sm text-gray-600">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                正在检测违禁词...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);
    }

    // 更新违禁词检测进度
    updateProhibitedWordsProgress(current, total, status) {
        const percentage = Math.round((current / total) * 100);
        $('#prohibitedWordsProgressBar').css('width', `${percentage}%`);
        $('#prohibitedWordsProgressText').text(`${current}/${total}`);
        $('#prohibitedWordsCurrentStatus').text(status);
    }

    // 关闭违禁词检测进度弹窗
    closeProhibitedWordsProgressModal() {
        $('#prohibitedWordsProgressModal').remove();
    }

    // 显示违禁词检测结果
    showProhibitedWordsResult(processedCount, totalProhibitedWords, foundWords, processedProducts, errorProducts = []) {
        const resultHtml = `
            <div id="prohibitedWordsResultModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">违禁词检测结果</h3>
                            <button onclick="$('#prohibitedWordsResultModal').remove()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>

                        <!-- 统计信息 -->
                        <div class="grid grid-cols-4 gap-4 mb-6">
                            <div class="bg-blue-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-blue-600">${processedCount}</div>
                                <div class="text-sm text-gray-600">检测产品数</div>
                            </div>
                            <div class="bg-red-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-red-600">${totalProhibitedWords}</div>
                                <div class="text-sm text-gray-600">发现违禁词数</div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-green-600">${processedProducts.length}</div>
                                <div class="text-sm text-gray-600">成功处理数</div>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-yellow-600">${errorProducts.length}</div>
                                <div class="text-sm text-gray-600">处理失败数</div>
                            </div>
                        </div>

                        <!-- 发现的违禁词列表 -->
                        ${foundWords.length > 0 ? `
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">发现的违禁词 (${foundWords.length}个):</h4>
                            <div class="bg-gray-50 p-3 rounded-lg max-h-32 overflow-y-auto">
                                <div class="flex flex-wrap gap-2">
                                    ${foundWords.map(word => `<span class="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded">${word}</span>`).join('')}
                                </div>
                            </div>
                        </div>
                        ` : ''}

                        <!-- 处理详情 -->
                        ${processedProducts.length > 0 ? `
                        <div class="mb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3">处理详情:</h4>
                            <div class="max-h-64 overflow-y-auto border rounded-lg">
                                <table class="w-full text-sm">
                                    <thead class="bg-gray-50 sticky top-0">
                                        <tr>
                                            <th class="px-3 py-2 text-left">产品ID</th>
                                            <th class="px-3 py-2 text-left">产品名称</th>
                                            <th class="px-3 py-2 text-left">发现违禁词</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${processedProducts.map(product => `
                                        <tr class="border-t">
                                            <td class="px-3 py-2">${product.productId}</td>
                                            <td class="px-3 py-2" title="${product.productName}">${product.productName ? (product.productName.length > 20 ? product.productName.substring(0, 20) + '...' : product.productName) : '--'}</td>
                                            <td class="px-3 py-2">
                                                <div class="flex flex-wrap gap-1">
                                                    ${product.foundWords.map(word => `<span class="bg-red-100 text-red-800 text-xs px-1 py-0.5 rounded">${word}</span>`).join('')}
                                                </div>
                                            </td>
                                        </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        ` : `
                        <div class="text-center py-8">
                            <i class="fas fa-check-circle text-4xl text-green-500 mb-3"></i>
                            <p class="text-lg text-gray-600">未发现违禁词，所有手卡内容符合规范！</p>
                        </div>
                        `}

                        <!-- 错误信息 -->
                        ${errorProducts.length > 0 ? `
                        <div class="mb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3">处理失败的产品 (${errorProducts.length}个):</h4>
                            <div class="max-h-32 overflow-y-auto border rounded-lg">
                                <table class="w-full text-sm">
                                    <thead class="bg-red-50 sticky top-0">
                                        <tr>
                                            <th class="px-3 py-2 text-left">产品ID</th>
                                            <th class="px-3 py-2 text-left">产品名称</th>
                                            <th class="px-3 py-2 text-left">错误信息</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${errorProducts.map(product => `
                                        <tr class="border-t">
                                            <td class="px-3 py-2">${product.productId}</td>
                                            <td class="px-3 py-2" title="${product.productName}">${product.productName ? (product.productName.length > 20 ? product.productName.substring(0, 20) + '...' : product.productName) : '--'}</td>
                                            <td class="px-3 py-2 text-red-600">${product.error}</td>
                                        </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        ` : ''}

                        <div class="flex justify-end">
                            <button onclick="$('#prohibitedWordsResultModal').remove()"
                                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(resultHtml);
    }
}

// 创建全局实例
const productListModal = new ProductListModal();

// 全局函数供HTML调用
function showProductList(liveId, anchorName = '') {
    productListModal.show(liveId, anchorName);
} 