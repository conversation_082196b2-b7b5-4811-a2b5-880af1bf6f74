<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务详情</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/jquery-3.5.0.min.js"></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/layer.3.5.1/layer.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        /* 主容器 */
        .main-container {
            max-width: 1600px;
            margin: 0 auto;
        }
        
        /* 卡片样式 */
        .content-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.06);
            border: 1px solid rgba(0,0,0,0.05);
            margin-bottom: 1.5rem;
        }
        
        .section-title {
            font-size: 1.2rem;
            color: #2d3748;
            margin-bottom: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            justify-content: space-between;
        }

        .section-title-left {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        /* 表格样式 */
        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        
        .detail-table th,
        .detail-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .detail-table th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.9rem;
        }
        
        .detail-table td {
            color: #2d3748;
            font-size: 0.9rem;
        }
        
        .detail-table tr:hover {
            background: #f8f9fa;
        }
        
        /* 状态标签 */
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }
        
        .status-pending { background: linear-gradient(135deg, #f6ad55 0%, #d69e2e 100%); }
        .status-running { background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%); }
        .status-completed { background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); }
        .status-failed { background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); }
        .status-cancelled { background: linear-gradient(135deg, #718096 0%, #4a5568 100%); }
        
        /* 进度条 */
        .progress-container {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        /* 按钮样式 */
        .btn {
            padding: 0.6rem 1.2rem;
            border-radius: 8px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .btn-small {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #edf2f7;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(72, 187, 120, 0.3);
        }
        
        /* 动作按钮区域 */
        .actions-section {
            text-align: center;
            margin-top: 2rem;
        }
        
        /* 刷新指示器 */
        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            display: none;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        /* 右上角按钮样式 */
        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 70px;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
        }
        
        .close-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .close-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        /* 错误消息 */
        .error-message {
            background: rgba(245, 101, 101, 0.1);
            color: #e53e3e;
            padding: 0.5rem;
            border-radius: 6px;
            border: 1px solid rgba(245, 101, 101, 0.2);
            font-size: 0.8rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .detail-table {
                font-size: 0.8rem;
            }
            
            .detail-table th,
            .detail-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 右上角按钮 -->
        <button class="refresh-btn" onclick="refreshData()" title="刷新">
            <i class="fas fa-sync"></i>
        </button>
        <button class="close-btn" onclick="closeWindow()" title="关闭">
            <i class="fas fa-times"></i>
        </button>
        
        <!-- 刷新指示器 -->
        <div id="refreshIndicator" class="refresh-indicator">
            <i class="fas fa-sync fa-spin"></i> 自动刷新中...
        </div>
        
        <!-- 任务基本信息 -->
        <div class="content-card">
            <div class="section-title">
                <i class="fas fa-info-circle"></i> 任务基本信息
            </div>
            <table class="detail-table" id="taskBasicInfo">
                <!-- 基本信息将通过JS动态生成 -->
            </table>
        </div>
        
        <!-- 任务进度 -->
        <div class="content-card">
            <div class="section-title">
                <i class="fas fa-chart-line"></i> 执行进度
            </div>
            <div id="taskProgress">
                <!-- 进度信息将通过JS动态生成 -->
            </div>
        </div>
        
        <!-- 子任务列表 -->
        <div class="content-card">
            <div class="section-title">
                <div class="section-title-left">
                    <i class="fas fa-list"></i> 子任务详情
                </div>
                <button class="btn btn-success btn-small" onclick="copyProductIds()" title="复制产品ID列表（按序号倒序）">
                    <i class="fas fa-copy"></i> 复制产品ID
                </button>
            </div>
            <table class="detail-table" id="subTaskTable">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>产品名称</th>
                        <th>产品ID</th>
                        <th>状态</th>
                        <th>开始时间</th>
                        <th>完成时间</th>
                    </tr>
                </thead>
                <tbody id="subTaskList">
                    <!-- 子任务列表将通过JS动态生成 -->
                </tbody>
            </table>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions-section">
            <button class="btn btn-primary" onclick="refreshData()">
                <i class="fas fa-sync"></i> 刷新
            </button>
            <button class="btn btn-secondary" onclick="closeWindow()">
                <i class="fas fa-times"></i> 关闭
            </button>
        </div>
    </div>

    <script>
        let taskId = null;
        let refreshTimer = null;
        let currentTaskDetails = [];
        
        // 初始化
        init();
        
        function init() {
            // 从URL参数获取任务ID
            const urlParams = new URLSearchParams(window.location.search);
            taskId = urlParams.get('taskId');
            
            if (!taskId) {
                layer.msg('缺少任务ID参数', {icon: 2});
                return;
            }
            
            loadTaskDetails();
            
            // 设置自动刷新
            startAutoRefresh();
        }
        
        // 加载任务详情
        async function loadTaskDetails() {
            try {
                showRefreshIndicator();
                
                const response = await fetch(`/api/task/${taskId}/details`, {
                    headers: getApiKeyHeader()
                });
                const result = await response.json();
                
                if (result.success) {
                    renderTaskDetails(result.task, result.details);
                } else {
                    layer.msg('加载任务详情失败: ' + result.message, {icon: 2});
                }
            } catch (error) {
                console.error('加载任务详情失败:', error);
                layer.msg('加载任务详情失败', {icon: 2});
            } finally {
                hideRefreshIndicator();
            }
        }
        
        // 渲染任务详情
        function renderTaskDetails(task, details) {
            // 保存当前任务详情数据，供复制功能使用
            currentTaskDetails = details;

            renderBasicInfo(task);
            renderProgress(task, details);
            renderSubTasks(details);

            // 根据任务状态决定是否继续自动刷新
            if (['pending', 'running'].includes(task.status)) {
                if (!refreshTimer) {
                    startAutoRefresh();
                }
            } else {
                stopAutoRefresh();
            }
        }
        
        // 渲染基本信息表格
        function renderBasicInfo(task) {
            const basicInfo = document.getElementById('taskBasicInfo');

            // 解析任务参数
            let taskParams = {};
            try {
                taskParams = task.task_params ? JSON.parse(task.task_params) : {};
            } catch (e) {
                console.warn('解析任务参数失败:', e);
            }

            basicInfo.innerHTML = `
                <tr>
                    <th>任务名称</th>
                    <td>${task.task_name}</td>
                    <th>任务类型</th>
                    <td>${getTaskTypeText(task.task_type)}</td>
                </tr>
                <tr>
                    <th>执行状态</th>
                    <td><span class="status-badge status-${task.status}">${getStatusText(task.status)}</span></td>
                    <th>主播名称</th>
                    <td>${task.anchor_name}</td>
                </tr>
                <tr>
                    <th>直播ID</th>
                    <td>${task.live_id || '无'}</td>
                    <th>优先级</th>
                    <td>${task.priority}</td>
                </tr>
                <tr>
                    <th>创建时间</th>
                    <td>${formatDateTime(task.created_at)}</td>
                    <th>开始时间</th>
                    <td>${task.started_at ? formatDateTime(task.started_at) : '未开始'}</td>
                </tr>
                <tr>
                    <th>完成时间</th>
                    <td>${task.completed_at ? formatDateTime(task.completed_at) : '未完成'}</td>
                    <th>重试次数</th>
                    <td>${task.retry_count}/${task.max_retries}</td>
                </tr>
                ${task.task_type === 'audio' && taskParams.concurrency ? `
                <tr>
                    <th>并发数</th>
                    <td>${taskParams.concurrency}</td>
                    <th>语速设置</th>
                    <td>${taskParams.randomSpeed ? '随机语速' : `固定语速 ${taskParams.speed || 1.2}`}</td>
                </tr>
                ` : ''}
                ${task.current_product_id ? `
                <tr>
                    <th>当前产品</th>
                    <td>${task.current_product_id}</td>
                    <th>当前序号</th>
                    <td>${task.current_sequence}/${task.task_count}</td>
                </tr>
                ` : ''}
                ${task.error_message ? `
                <tr>
                    <th>错误信息</th>
                    <td colspan="3">
                        <div class="error-message">${task.error_message}</div>
                    </td>
                </tr>
                ` : ''}
            `;
        }
        
        // 渲染进度信息
        function renderProgress(task, details) {
            const progressSection = document.getElementById('taskProgress');
            
            const percentage = task.progress_percentage || 0;
            const completedCount = task.success_count + task.failed_count;
            
            progressSection.innerHTML = `
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${percentage}%">
                        ${percentage}%
                    </div>
                </div>
                <table class="detail-table">
                    <tr>
                        <th>总任务数</th>
                        <td>${task.task_count}</td>
                        <th>已完成</th>
                        <td style="color: #48bb78; font-weight: 600;">${completedCount}</td>
                        <th>待处理</th>
                        <td style="color: #f6ad55; font-weight: 600;">${task.task_count - completedCount}</td>
                    </tr>
                    <tr>
                        <th>成功</th>
                        <td style="color: #48bb78; font-weight: 600;">${task.success_count}</td>
                        <th>失败</th>
                        <td style="color: #f56565; font-weight: 600;">${task.failed_count}</td>
                        <th>成功率</th>
                        <td style="font-weight: 600;">${completedCount > 0 ? Math.round(task.success_count / completedCount * 100) : 0}%</td>
                    </tr>
                </table>
            `;
        }
        
        // 渲染子任务列表
        function renderSubTasks(details) {
            const subTaskList = document.getElementById('subTaskList');
            
            if (details.length === 0) {
                subTaskList.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #718096;">暂无子任务数据</td></tr>';
                return;
            }
            
            subTaskList.innerHTML = details.map(detail => `
                <tr>
                    <td>${detail.sequence_number}</td>
                    <td>${detail.product_name}</td>
                    <td>${detail.product_id}</td>
                    <td><span class="status-badge status-${detail.status}">${getStatusText(detail.status)}</span></td>
                    <td>${detail.started_at ? formatDateTime(detail.started_at) : '未开始'}</td>
                    <td>${detail.completed_at ? formatDateTime(detail.completed_at) : '未完成'}</td>
                </tr>
            `).join('');
        }
        
        // 刷新数据
        window.refreshData = function() {
            loadTaskDetails();
        };
        
        // 关闭窗口
        window.closeWindow = function() {
            // 尝试多种关闭方式
            try {
                // 如果是在 layer 弹窗中
                if (typeof parent.layer !== 'undefined') {
                    parent.layer.closeAll();
                    return;
                }
                
                // 如果是通过 JavaScript 打开的窗口
                if (window.opener) {
                    window.close();
                    return;
                }
                
                // 否则返回上一页
                history.back();
            } catch (error) {
                // 如果所有方法都失败，则返回上一页
                history.back();
            }
        };
        
        // 开始自动刷新
        function startAutoRefresh() {
            if (refreshTimer) return;
            
            refreshTimer = setInterval(() => {
                loadTaskDetails();
            }, 3000);
        }
        
        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshTimer) {
                clearInterval(refreshTimer);
                refreshTimer = null;
            }
        }
        
        // 显示刷新指示器
        function showRefreshIndicator() {
            document.getElementById('refreshIndicator').style.display = 'block';
        }
        
        // 隐藏刷新指示器
        function hideRefreshIndicator() {
            setTimeout(() => {
                document.getElementById('refreshIndicator').style.display = 'none';
            }, 500);
        }
        
        // 工具函数
        function getStatusText(status) {
            const statusMap = {
                'pending': '待执行',
                'running': '执行中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }
        
        function getTaskTypeText(type) {
            const typeMap = {
                'hand_card': '手卡提取',
                'audio': '音频生成',
                'live': '直播任务'
            };
            return typeMap[type] || type;
        }
        
        function formatDateTime(dateStr) {
            if (!dateStr) return '';
            return new Date(dateStr).toLocaleString('zh-CN');
        }
        
        function getApiKeyHeader() {
            const apiKey = localStorage.getItem('apiKey') || '';
            return {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json'
            };
        }
        
        // 复制产品ID列表（按序号倒序）
        window.copyProductIds = function() {
            if (!currentTaskDetails || currentTaskDetails.length === 0) {
                layer.msg('暂无产品数据可复制', {icon: 2});
                return;
            }

            try {
                // 按序号倒序排列并提取产品ID
                const productIds = currentTaskDetails
                    .filter(detail => detail.product_id) // 过滤掉没有产品ID的记录
                    .sort((a, b) => (b.sequence_number || 0) - (a.sequence_number || 0)) // 按序号倒序
                    .map(detail => detail.product_id);

                if (productIds.length === 0) {
                    layer.msg('没有找到有效的产品ID', {icon: 2});
                    return;
                }

                // 将产品ID列表转换为字符串（每行一个）
                const productIdText = productIds.join('\n');

                // 复制到剪贴板
                if (navigator.clipboard && window.isSecureContext) {
                    // 使用现代的 Clipboard API
                    navigator.clipboard.writeText(productIdText).then(() => {
                        layer.msg(`已复制 ${productIds.length} 个产品ID到剪贴板`, {icon: 1});
                    }).catch(err => {
                        console.error('复制失败:', err);
                        fallbackCopyTextToClipboard(productIdText, productIds.length);
                    });
                } else {
                    // 降级方案
                    fallbackCopyTextToClipboard(productIdText, productIds.length);
                }
            } catch (error) {
                console.error('复制产品ID失败:', error);
                layer.msg('复制失败，请重试', {icon: 2});
            }
        };

        // 降级复制方案
        function fallbackCopyTextToClipboard(text, count) {
            try {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);

                if (successful) {
                    layer.msg(`已复制 ${count} 个产品ID到剪贴板`, {icon: 1});
                } else {
                    layer.msg('复制失败，请手动复制', {icon: 2});
                }
            } catch (err) {
                console.error('降级复制方案也失败:', err);
                layer.msg('复制失败，请手动复制', {icon: 2});
            }
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    </script>
</body>
</html>
