import crypto from 'crypto';
import { database } from './database.js';
import { handleTaobaoApiResponse } from './utils/response-handler.js';

/**
 * 批量上品工具
 */
class BatchAddProducts {
    constructor() {
        this.appKey = '12574478';
        this.api = 'mtop.mediaplatform.video.additem.batch';
        this.version = '1.0';
    }

    /**
     * MD5哈希函数
     * @param {string} string 
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string, 'utf8').digest('hex');
    }

    /**
     * 生成签名
     * @param {string} h5Token 
     * @param {number} timestamp 
     * @param {object} data 
     * @returns {string}
     */
    generateSign(h5Token, timestamp, data) {
        const dataStr = JSON.stringify(data);
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
        return this.md5(signString);
    }

    /**
     * 构建请求URL
     * @param {string} h5Token 
     * @param {object} data 
     * @returns {object}
     */
    buildRequestUrl(h5Token, data) {
        const timestamp = Date.now();
        const sign = this.generateSign(h5Token, timestamp, data);
        
        const params = new URLSearchParams({
            jsv: '2.7.2',
            appKey: this.appKey,
            t: timestamp.toString(),
            sign: sign,
            api: this.api,
            v: this.version,
            type: 'originaljson',
            dataType: 'json',
            preventFallback: 'true'
        });

        return {
            url: `https://h5api.m.taobao.com/h5/${this.api}/${this.version}/?${params.toString()}`,
            timestamp,
            sign
        };
    }

    /**
     * 批量添加产品到直播间
     * @param {string} liveId 直播ID
     * @param {Array} productIds 产品ID数组
     * @param {string} anchorName 主播名称
     * @param {string} h5Token h5Token
     * @param {string} cookie 主播cookie
     * @returns {Promise<Object>} 添加结果
     */
    async batchAddProducts(liveId, productIds, anchorName, h5Token, cookie) {
        try {
            console.log(`🚀 开始批量添加产品 - 直播ID: ${liveId}, 产品数量: ${productIds.length}`);

            // 构建批量参数
            const batchParams = productIds.map(itemId => ({
                itemId: parseInt(itemId),
                publishParam: {
                    right: "",
                    rightType: "1",
                    benefitCodes: "[]",
                    benefitDesc: "",
                    itemExtendVal: "{}",
                    pageSource: "",
                    sign: "",
                    tabType: "9",
                    itemCategoryIdList: "[]"
                }
            }));

            const data = {
                liveId: liveId,
                batchParams: JSON.stringify(batchParams)
            };

            // 构建请求
            const { url } = this.buildRequestUrl(h5Token, data);
            console.log(`📡 发送批量添加请求 - URL: ${url.substring(0, 100)}...`);

            const headers = {
                'accept': 'application/json',
                'accept-language': 'zh-CN,zh;q=0.9',
                'content-type': 'application/x-www-form-urlencoded',
                'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                'referer': 'https://liveplatform.taobao.com/',
                'cookie': cookie
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: `data=${encodeURIComponent(JSON.stringify(data))}`,
                mode: 'cors',
                credentials: 'include'
            });

            console.log(`📊 批量添加响应状态: ${response.status}`);

            if (!response.ok) {
                return {
                    success: false,
                    error: `HTTP错误: ${response.status} ${response.statusText}`
                };
            }

            // 使用统一的响应处理器
            const responseResult = await handleTaobaoApiResponse(response, anchorName, cookie);

            if (responseResult.success) {
                // 解析详细的成功/失败结果
                const detailResult = this.parseDetailedResult(responseResult.data, productIds);

                console.log(`✅ 批量添加完成 - 直播ID: ${liveId}, 总数: ${detailResult.totalCount}, 成功: ${detailResult.successCount}, 失败: ${detailResult.failedCount}`);

                if (detailResult.failedItems.length > 0) {
                    console.log(`❌ 失败的产品详情:`);
                    detailResult.failedItems.forEach(item => {
                        console.log(`   产品ID: ${item.itemId}, 错误: ${item.errorCode} - ${item.msgInfo}`);
                    });
                }

                return {
                    success: true,
                    data: responseResult.data,
                    totalCount: detailResult.totalCount,
                    successCount: detailResult.successCount,
                    failedCount: detailResult.failedCount,
                    successItems: detailResult.successItems,
                    failedItems: detailResult.failedItems,
                    cookieUpdated: responseResult.cookieUpdated,
                    updatedCookie: responseResult.updatedCookie
                };
            } else {
                console.error(`❌ 批量添加失败 - 直播ID: ${liveId}, 错误: ${responseResult.error}`);
                return {
                    success: false,
                    error: responseResult.error,
                    errorType: responseResult.errorType,
                    totalCount: productIds.length,
                    successCount: 0,
                    failedCount: productIds.length,
                    cookieUpdated: responseResult.cookieUpdated,
                    updatedCookie: responseResult.updatedCookie
                };
            }

        } catch (error) {
            console.error(`💥 批量添加异常 - 直播ID: ${liveId}, 错误:`, error);
            return {
                success: false,
                error: error.message || '未知错误'
            };
        }
    }

    /**
     * 解析详细的批量添加结果
     * @param {Object} data 响应数据
     * @param {Array} productIds 产品ID数组
     * @returns {Object} 解析结果
     */
    parseDetailedResult(data, productIds) {
        const result = {
            totalCount: productIds.length,
            successCount: 0,
            failedCount: 0,
            successItems: [],
            failedItems: []
        };

        if (data && data.result && Array.isArray(data.result)) {
            data.result.forEach(item => {
                if (item.isSuccess === true) {
                    result.successCount++;
                    result.successItems.push({
                        itemId: item.itemId,
                        errorCode: item.errorCode,
                        msgInfo: item.msgInfo
                    });
                } else {
                    result.failedCount++;
                    result.failedItems.push({
                        itemId: item.itemId,
                        errorCode: item.errorCode,
                        msgInfo: item.msgInfo
                    });
                }
            });
        } else {
            // 如果没有详细结果，假设全部成功
            result.successCount = productIds.length;
            result.failedCount = 0;
        }

        return result;
    }

    /**
     * 分批处理产品添加
     * @param {string} liveId 直播ID
     * @param {Array} productIds 产品ID数组
     * @param {string} anchorName 主播名称
     * @param {string} h5Token h5Token
     * @param {string} cookie 主播cookie
     * @param {number} batchSize 每批数量，默认200
     * @param {number} interval 批次间隔毫秒数，默认2000
     * @param {Function} progressCallback 进度回调函数
     * @returns {Promise<Object>} 处理结果
     */
    async batchAddProductsWithPaging(liveId, productIds, anchorName, h5Token, cookie, batchSize = 200, interval = 2000, progressCallback = null) {
        try {
            console.log(`🎯 开始分批添加产品 - 总数: ${productIds.length}, 每批: ${batchSize}, 间隔: ${interval}ms`);

            const batches = [];
            for (let i = 0; i < productIds.length; i += batchSize) {
                batches.push(productIds.slice(i, i + batchSize));
            }

            let totalSuccess = 0;
            let totalFailed = 0;
            const failedBatches = [];
            const allSuccessItems = [];
            const allFailedItems = [];
            let currentCookie = cookie;

            for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                console.log(`📦 处理第 ${i + 1}/${batches.length} 批，包含 ${batch.length} 个产品`);

                try {
                    const result = await this.batchAddProducts(liveId, batch, anchorName, h5Token, currentCookie);

                    if (result.success) {
                        const batchSuccess = result.successCount || 0;
                        const batchFailed = result.failedCount || 0;

                        totalSuccess += batchSuccess;
                        totalFailed += batchFailed;

                        // 收集成功和失败的产品详情
                        if (result.successItems) {
                            allSuccessItems.push(...result.successItems);
                        }
                        if (result.failedItems) {
                            allFailedItems.push(...result.failedItems);
                        }

                        console.log(`✅ 第 ${i + 1} 批完成 - 成功: ${batchSuccess}, 失败: ${batchFailed}`);

                        // 调用进度回调
                        if (progressCallback) {
                            const progress = Math.round(((i + 1) / batches.length) * 100);
                            progressCallback({
                                currentBatch: i + 1,
                                totalBatches: batches.length,
                                progress: progress,
                                currentSuccess: totalSuccess,
                                currentFailed: totalFailed,
                                batchSuccess: batchSuccess,
                                batchFailed: batchFailed
                            });
                        }

                        // 如果cookie有更新，使用新的cookie
                        if (result.cookieUpdated && result.updatedCookie) {
                            currentCookie = result.updatedCookie;
                        }
                    } else {
                        totalFailed += batch.length;
                        failedBatches.push({
                            batchIndex: i + 1,
                            productIds: batch,
                            error: result.error
                        });
                        console.log(`❌ 第 ${i + 1} 批失败 - 错误: ${result.error}`);
                    }

                 

                } catch (error) {
                    console.error(`💥 第 ${i + 1} 批处理异常:`, error);
                    totalFailed += batch.length;
                    failedBatches.push({
                        batchIndex: i + 1,
                        productIds: batch,
                        error: error.message
                    });
                }
            }

            console.log(`🏁 分批添加完成 - 成功: ${totalSuccess}, 失败: ${totalFailed}`);

            return {
                success: true,
                totalCount: productIds.length,
                successCount: totalSuccess,
                failedCount: totalFailed,
                successItems: allSuccessItems,
                failedItems: allFailedItems,
                failedBatches: failedBatches,
                cookieUpdated: currentCookie !== cookie,
                updatedCookie: currentCookie
            };

        } catch (error) {
            console.error(`💥 分批添加异常:`, error);
            return {
                success: false,
                error: error.message || '未知错误',
                totalCount: productIds.length,
                successCount: 0,
                failedCount: productIds.length
            };
        }
    }
}

export default BatchAddProducts;
