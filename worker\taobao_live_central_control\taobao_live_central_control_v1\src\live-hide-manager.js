import crypto from 'crypto';
import { database } from './database.js';

/**
 * 直播场次隐藏管理器
 */
class LiveHideManager {
    constructor() {
        this.appKey = '12574478';
        this.jsv = '2.7.4';
        this.apiName = 'mtop.taobao.tblive.portal.live.replay.hide.set';
        this.apiVersion = '1.0';
    }

    /**
     * MD5哈希函数
     * @param {string} string 
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string, 'utf8').digest('hex');
    }

    /**
     * 生成签名
     * @param {string} h5Token 
     * @param {number} timestamp 
     * @param {object} data 
     * @returns {string}
     */
    generateSign(h5Token, timestamp, data) {
        const dataStr = JSON.stringify(data);
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
        return this.md5(signString);
    }

    /**
     * 从cookie中提取h5Token
     * @param {string} cookie 
     * @returns {string|null}
     */
    extractH5Token(cookie) {
        try {
            const cookies = cookie.split(';');
            for (let cookieItem of cookies) {
                const [name, value] = cookieItem.trim().split('=');
                if (name === '_m_h5_tk' && value) {
                    return value.split('_')[0];
                }
            }
        } catch (e) {
            console.error('提取h5Token失败:', e);
        }
        return null;
    }

    /**
     * 设置单个直播场次的显示状态
     * @param {string} liveId
     * @param {string} cookie
     * @param {number} hide 0=展示, 1=隐藏
     * @returns {Promise<boolean>}
     */
    async setSingleLiveStatus(liveId, cookie, hide = 1) {
        try {
            const actionText = hide === 1 ? '隐藏' : '展示';
            console.log(`🔒 开始${actionText}直播场次 - liveId: ${liveId}, hide: ${hide}`);

            // 提取h5Token
            const h5Token = this.extractH5Token(cookie);
            if (!h5Token) {
                console.error(`❌ 无法从cookie中提取h5Token - liveId: ${liveId}`);
                return false;
            }

            // 构建请求参数
            const timestamp = Date.now().toString();
            const data = {
                liveId: liveId,
                hide: hide
            };

            const sign = this.generateSign(h5Token, timestamp, data);
            const dataStr = JSON.stringify(data);

            console.log(`📝 ${actionText}请求参数 - liveId: ${liveId}, timestamp: ${timestamp}, hide: ${hide}`);
            console.log(`🔑 签名字符串: ${h5Token}&${timestamp}&${this.appKey}&${dataStr}`);
            console.log(`✍️ 签名结果: ${sign}`);

            // 构建URL
            const params = new URLSearchParams({
                jsv: this.jsv,
                appKey: this.appKey,
                t: timestamp,
                sign: sign,
                api: this.apiName,
                v: this.apiVersion,
                preventFallback: 'true',
                type: 'jsonp',
                dataType: 'jsonp',
                callback: `mtopjsonp${Math.floor(Math.random() * 10000)}`,
                data: dataStr
            });

            const url = `https://h5api.m.taobao.com/h5/${this.apiName}/${this.apiVersion}/?${params}`;
            console.log(`🌐 ${actionText}请求URL: ${url}`);

            // 发送请求
            const headers = {
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'script',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'same-site',
                'Referer': 'https://liveplatform.taobao.com/restful/index/live/list',
                'Referrer-Policy': 'no-referrer-when-downgrade',
                'Cookie': cookie
            };

            const response = await fetch(url, {
                method: 'GET',
                headers: headers,
                mode: 'cors',
                credentials: 'include'
            });

            console.log(`📊 ${actionText}响应状态 - liveId: ${liveId}, status: ${response.status}`);

            if (!response.ok) {
                console.error(`❌ ${actionText}请求失败 - liveId: ${liveId}, status: ${response.status}`);
                return false;
            }

            const text = await response.text();
            console.log(`📄 ${actionText}响应内容 - liveId: ${liveId}: ${text.substring(0, 200)}...`);

            // 解析JSONP响应
            const jsonpMatch = text.match(/mtopjsonp\d+\((.*)\)/);
            if (!jsonpMatch) {
                console.error(`❌ 无法解析JSONP响应 - liveId: ${liveId}`);
                return false;
            }

            const responseData = JSON.parse(jsonpMatch[1]);
            console.log(`📋 解析后的响应数据 - liveId: ${liveId}:`, responseData);

            // 检查响应结果
            if (responseData.ret && responseData.ret[0] === 'SUCCESS::调用成功') {
                console.log(`✅ 直播场次${actionText}成功 - liveId: ${liveId}`);
                return true;
            } else {
                console.error(`❌ 直播场次${actionText}失败 - liveId: ${liveId}, 错误:`, responseData.ret);
                return false;
            }

        } catch (error) {
            console.error(`💥 ${actionText}直播场次异常 - liveId: ${liveId}, 错误:`, error.message);
            return false;
        }
    }

    /**
     * 隐藏单个直播场次（向后兼容）
     * @param {string} liveId
     * @param {string} cookie
     * @returns {Promise<boolean>}
     */
    async hideSingleLive(liveId, cookie) {
        return this.setSingleLiveStatus(liveId, cookie, 1);
    }

    /**
     * 展示单个直播场次
     * @param {string} liveId
     * @param {string} cookie
     * @returns {Promise<boolean>}
     */
    async showSingleLive(liveId, cookie) {
        return this.setSingleLiveStatus(liveId, cookie, 0);
    }

    /**
     * 批量隐藏已展示的直播场次
     * @returns {Promise<{success: boolean, hiddenCount: number, errors: Array}>}
     */
    async hideAllDisplayedLives() {
        try {
            console.log('🚀 开始批量隐藏已展示的直播场次');

            // 查询所有已展示的直播场次
            const queryResult = await database.all(`
                SELECT DISTINCT lp.live_id, lp.anchor_name, a.anchor_cookie
                FROM live_plans lp
                JOIN anchors a ON lp.anchor_name = a.anchor_name
                WHERE lp.valid_status = '已展示' AND lp.live_status='已结束'
                AND a.status = 'active'
                AND a.anchor_cookie IS NOT NULL
                AND a.anchor_cookie != ''
                ORDER BY lp.live_date DESC
            `);

            console.log('📊 数据库查询结果:', queryResult);
            console.log('📊 查询结果类型:', typeof queryResult);

            // 从查询结果中提取数组
            const livesArray = queryResult.results || [];
            console.log('📊 提取的数组:', livesArray);
            console.log('📊 是否为数组:', Array.isArray(livesArray));
            console.log(`📊 找到 ${livesArray.length} 个已展示的直播场次`);

            if (livesArray.length === 0) {
                return {
                    success: true,
                    hiddenCount: 0,
                    errors: ['没有找到需要隐藏的直播场次']
                };
            }

            let hiddenCount = 0;
            const errors = [];

            // 逐个隐藏直播场次
            for (const live of livesArray) {
                try {
                    console.log(`🔄 处理直播场次 - liveId: ${live.live_id}, anchor: ${live.anchor_name}`);

                    if (!live.live_id) {
                        console.error(`❌ 直播ID为空 - anchor: ${live.anchor_name}`);
                        errors.push(`直播ID为空: ${live.anchor_name}`);
                        continue;
                    }

                    if (!live.anchor_cookie) {
                        console.error(`❌ 主播cookie为空 - liveId: ${live.live_id}`);
                        errors.push(`主播cookie为空: ${live.live_id} (${live.anchor_name})`);
                        continue;
                    }

                    const success = await this.hideSingleLive(live.live_id, live.anchor_cookie);
                    if (success) {
                        hiddenCount++;
                        // 更新数据库中的状态
                        await database.run(
                            'UPDATE live_plans SET valid_status = ? WHERE live_id = ?',
                            ['已隐藏', live.live_id]
                        );
                        console.log(`✅ 已更新数据库状态 - liveId: ${live.live_id}`);
                    } else {
                        errors.push(`隐藏失败: ${live.live_id} (${live.anchor_name})`);
                    }
                } catch (error) {
                    console.error(`处理直播场次失败 - liveId: ${live.live_id}:`, error);
                    errors.push(`处理失败: ${live.live_id} (${live.anchor_name}) - ${error.message}`);
                }

                // 添加延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            console.log(`🎯 批量隐藏完成 - 成功: ${hiddenCount}, 失败: ${errors.length}`);

            return {
                success: true,
                hiddenCount,
                errors
            };

        } catch (error) {
            console.error('💥 批量隐藏直播场次异常:', error);
            return {
                success: false,
                hiddenCount: 0,
                errors: [error.message]
            };
        }
    }

    /**
     * 批量设置指定主播的直播场次显示状态
     * @param {Array<string>} anchorNames 主播名称数组
     * @param {number} hide 0=展示, 1=隐藏
     * @returns {Promise<{success: boolean, hiddenCount: number, showCount: number, errors: Array}>}
     */
    async setSelectedAnchorsLivesStatus(anchorNames, hide = 1) {
        try {
            const actionText = hide === 1 ? '隐藏' : '展示';
            const currentStatus = hide === 1 ? '已展示' : '已隐藏';
            const newStatus = hide === 1 ? '已隐藏' : '已展示';

            console.log(`🚀 开始批量${actionText}指定主播的${currentStatus}直播场次:`, anchorNames);

            if (!anchorNames || anchorNames.length === 0) {
                return {
                    success: true,
                    hiddenCount: 0,
                    showCount: 0,
                    errors: [`没有指定要${actionText}的主播`]
                };
            }

            // 构建查询条件
            const placeholders = anchorNames.map(() => '?').join(',');
            const queryResult = await database.all(`
                SELECT DISTINCT lp.live_id, lp.anchor_name, a.anchor_cookie
                FROM live_plans lp
                JOIN anchors a ON lp.anchor_name = a.anchor_name
                WHERE lp.valid_status = '${currentStatus}' AND lp.live_status='已结束'
                AND lp.anchor_name IN (${placeholders})
                AND a.status = 'active'
                AND a.anchor_cookie IS NOT NULL
                AND a.anchor_cookie != ''
                ORDER BY lp.live_date DESC
            `, anchorNames);

            console.log('📊 数据库查询结果:', queryResult);

            // 从查询结果中提取数组
            const livesArray = queryResult.results || [];
            console.log(`📊 找到 ${livesArray.length} 个指定主播的${currentStatus}直播场次`);

            if (livesArray.length === 0) {
                return {
                    success: true,
                    hiddenCount: 0,
                    showCount: 0,
                    errors: [`没有找到指定主播的需要${actionText}的直播场次`]
                };
            }

            let operationCount = 0;
            const errors = [];

            // 逐个处理直播场次
            for (const live of livesArray) {
                try {
                    console.log(`🔄 处理直播场次 - liveId: ${live.live_id}, anchor: ${live.anchor_name}`);

                    if (!live.live_id) {
                        console.error(`❌ 直播ID为空 - anchor: ${live.anchor_name}`);
                        errors.push(`直播ID为空: ${live.anchor_name}`);
                        continue;
                    }

                    if (!live.anchor_cookie) {
                        console.error(`❌ 主播cookie为空 - liveId: ${live.live_id}`);
                        errors.push(`主播cookie为空: ${live.live_id} (${live.anchor_name})`);
                        continue;
                    }

                    const success = await this.setSingleLiveStatus(live.live_id, live.anchor_cookie, hide);
                    if (success) {
                        operationCount++;
                        // 更新数据库中的状态
                        await database.run(
                            'UPDATE live_plans SET valid_status = ? WHERE live_id = ?',
                            [newStatus, live.live_id]
                        );
                        console.log(`✅ 已更新数据库状态 - liveId: ${live.live_id}`);
                    } else {
                        errors.push(`${actionText}失败: ${live.live_id} (${live.anchor_name})`);
                    }
                } catch (error) {
                    console.error(`处理直播场次失败 - liveId: ${live.live_id}:`, error);
                    errors.push(`处理失败: ${live.live_id} (${live.anchor_name}) - ${error.message}`);
                }

                // 添加延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            console.log(`🎯 批量${actionText}完成 - 成功: ${operationCount}, 失败: ${errors.length}`);

            const result = {
                success: true,
                errors
            };

            // 根据操作类型设置对应的计数字段
            if (hide === 1) {
                result.hiddenCount = operationCount;
                result.showCount = 0;
            } else {
                result.hiddenCount = 0;
                result.showCount = operationCount;
            }

            return result;

        } catch (error) {
            console.error(`💥 批量${hide === 1 ? '隐藏' : '展示'}指定主播直播场次异常:`, error);
            return {
                success: false,
                hiddenCount: 0,
                showCount: 0,
                errors: [error.message]
            };
        }
    }

    /**
     * 批量隐藏指定主播的已展示直播场次（向后兼容）
     * @param {Array<string>} anchorNames 主播名称数组
     * @returns {Promise<{success: boolean, hiddenCount: number, errors: Array}>}
     */
    async hideSelectedAnchorsLives(anchorNames) {
        return this.setSelectedAnchorsLivesStatus(anchorNames, 1);
    }

    /**
     * 批量展示指定主播的已隐藏直播场次
     * @param {Array<string>} anchorNames 主播名称数组
     * @returns {Promise<{success: boolean, showCount: number, errors: Array}>}
     */
    async showSelectedAnchorsLives(anchorNames) {
        return this.setSelectedAnchorsLivesStatus(anchorNames, 0);
    }
}

export { LiveHideManager };
