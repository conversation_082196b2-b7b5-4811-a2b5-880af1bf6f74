/**
 * API响应工具函数
 */

/**
 * 发送成功响应
 * @param {object} res - Express响应对象
 * @param {string} message - 成功消息
 * @param {object} data - 响应数据
 */
export function sendSuccessResponse(res, message, data = {}) {
  res.json({
    success: true,
    message,
    data
  });
}

/**
 * 发送错误响应
 * @param {object} res - Express响应对象
 * @param {number} statusCode - HTTP状态码
 * @param {string} error - 错误代码
 * @param {string} message - 错误消息
 */
export function sendErrorResponse(res, statusCode, error, message) {
  res.status(statusCode).json({
    success: false,
    error,
    message
  });
}

/**
 * 发送批量操作结果响应
 * @param {object} res - Express响应对象
 * @param {string} message - 操作消息
 * @param {object} stats - 统计信息
 * @param {Array} failedItems - 失败项目列表（可选）
 */
export function sendBatchOperationResponse(res, message, stats, failedItems = null) {
  const data = { ...stats };
  if (failedItems && failedItems.length > 0) {
    data.failedItems = failedItems;
  }
  
  sendSuccessResponse(res, message, data);
}

/**
 * 处理API错误并发送响应
 * @param {object} res - Express响应对象
 * @param {Error} error - 错误对象
 * @param {string} operation - 操作名称
 */
export function handleApiError(res, error, operation) {
  console.error(`Error in ${operation}:`, error);
  sendErrorResponse(res, 500, "Internal server error", `${operation}失败: ${error.message}`);
}

/**
 * 验证必需参数
 * @param {object} params - 参数对象
 * @param {Array} requiredFields - 必需字段列表
 * @param {object} res - Express响应对象
 * @returns {boolean} - 是否验证通过
 */
export function validateRequiredParams(params, requiredFields, res) {
  for (const field of requiredFields) {
    if (!params[field]) {
      sendErrorResponse(res, 400, "Missing required parameters", `${field}是必需的`);
      return false;
    }
  }
  return true;
}
