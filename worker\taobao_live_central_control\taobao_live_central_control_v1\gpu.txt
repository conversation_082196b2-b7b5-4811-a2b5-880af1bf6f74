创建音频任务新增功能：
1. ui上新增勾选按钮（自动创建gpu），添加创建gpu功能，调用创建gpu接口，创建完任务后，查询gpu账户中第一条信息获取image_id，替换到body中的image，name使用当前 主播名字+"-使用中"，其他参数保持不变，接口写法方式参考其他接口。
2. 创建完任务调用完创建接口后，如果响应内容是"success": false，则一直创建，设置0.1秒一次，直到创建成功为止，创建完后，一直循环调用https://容器ID-9880.container.x-gpu.com,1秒1次，如果响应是200，延迟5秒后 则再进行多线程生成音频任务

Headers中使用api_key，写到Authorization中，Bearer api_key


curl -X POST "https://api.xiangongyun.com/open/instance/deploy" \
   \
  -d {"gpu_model":"NVIDIA GeForce RTX 4090 D","gpu_count":1,"data_center_id":1,"image":"260cd46a-12aa-44ed-b47a-88f770b49041","image_type":"private","storage":false,"storage_mount_path":"/root/cloud","system_disk_expand":false,"system_disk_expand_size":0,"name":""}


错误响应
 {
  "code": 1000,
  "msg": "可用GPU不足",
  "success": false
}
  正确响应：
{"code":200,"data":{"id":"jrxnpbzv83kig8hz"},"success":true}


3.创建成功后，记录id到任务表的task_param字段中，等待最后销毁镜像的时候使用，新加一个根据实例id销毁镜像的方法，使用的是api_key,而不是token，请记住
fetch("https://api.xiangongyun.com/open/instance/destroy", {
  "headers": {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "authorization": "Bearer vvmgf5axe56gg2eqjb5qdxjuqvhfvgl2woqgn4gg",
    "content-type": "application/json",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-site"
  },
  "referrer": "https://api-playground.xiangongyun.com/",
  "referrerPolicy": "strict-origin-when-cross-origin",
  "body": "{\"id\":\"jrxnpbzv83kig8hz\"}",
  "method": "POST",
  "mode": "cors",
  "credentials": "include"
});