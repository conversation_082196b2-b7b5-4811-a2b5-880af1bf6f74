/**
 * 前端通用工具模块
 * 包含常用的工具函数和常量定义
 */

// 任务状态映射
window.TASK_STATUS_MAP = {
    'pending': '待执行',
    'running': '执行中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消',
    'paused': '已暂停'
};

// 任务类型映射
window.TASK_TYPE_MAP = {
    'hand_card': '手卡提取',
    'audio': '音频生成',
    'live': '直播任务'
};

// 任务类型CSS类映射
window.TASK_TYPE_CLASS_MAP = {
    'hand_card': 'hand-card',
    'audio': 'audio',
    'live': 'live'
};

/**
 * 获取任务状态文本
 * @param {string} status 状态值
 * @returns {string} 状态文本
 */
window.getTaskStatusText = function(status) {
    return window.TASK_STATUS_MAP[status] || status;
};

/**
 * 获取任务类型文本
 * @param {string} type 类型值
 * @returns {string} 类型文本
 */
window.getTaskTypeText = function(type) {
    return window.TASK_TYPE_MAP[type] || type;
};

/**
 * 获取任务类型CSS类
 * @param {string} type 类型值
 * @returns {string} CSS类名
 */
window.getTaskTypeClass = function(type) {
    return window.TASK_TYPE_CLASS_MAP[type] || '';
};

/**
 * 格式化日期时间
 * @param {string} dateStr 日期字符串
 * @returns {string} 格式化后的时间
 */
window.formatDateTime = function(dateStr) {
    if (!dateStr) return '';
    try {
        return new Date(dateStr).toLocaleString('zh-CN');
    } catch (error) {
        return dateStr;
    }
};

/**
 * 获取API Key头部
 * @returns {Object} 包含API Key的头部对象
 */
window.getApiKeyHeader = function() {
    const apiKey = window.getCookie('api_key') || '';
    return {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
    };
};

/**
 * 获取Cookie值
 * @param {string} name Cookie名称
 * @returns {string|null} Cookie值
 */
window.getCookie = function(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
};

/**
 * 显示加载状态
 * @param {string} message 加载消息
 * @param {boolean} show 是否显示
 */
window.showLoading = function(message = '加载中...', show = true) {
    if (show) {
        if (typeof layer !== 'undefined') {
            window.loadingIndex = layer.load(1, { content: message });
        }
    } else {
        if (typeof layer !== 'undefined' && window.loadingIndex) {
            layer.close(window.loadingIndex);
            window.loadingIndex = null;
        }
    }
};

/**
 * 显示消息提示
 * @param {string} message 消息内容
 * @param {string} type 消息类型：success, error, warning, info
 */
window.showMessage = function(message, type = 'info') {
    if (typeof layer !== 'undefined') {
        const iconMap = {
            'success': 1,
            'error': 2,
            'warning': 3,
            'info': 6
        };
        layer.msg(message, { icon: iconMap[type] || 6 });
    } else {
        alert(message);
    }
};

/**
 * 确认对话框
 * @param {string} message 确认消息
 * @param {Function} onConfirm 确认回调
 * @param {Function} onCancel 取消回调
 */
window.showConfirm = function(message, onConfirm, onCancel) {
    if (typeof layer !== 'undefined') {
        layer.confirm(message, {icon: 3}, function(index) {
            if (onConfirm) onConfirm();
            layer.close(index);
        }, function() {
            if (onCancel) onCancel();
        });
    } else {
        if (confirm(message)) {
            if (onConfirm) onConfirm();
        } else {
            if (onCancel) onCancel();
        }
    }
};

/**
 * 统一的API请求函数
 * @param {string} url 请求URL
 * @param {Object} options 请求选项
 * @returns {Promise} 请求Promise
 */
window.apiRequest = async function(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: window.getApiKeyHeader(),
        ...options
    };

    try {
        const response = await fetch(url, defaultOptions);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        return result;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
};

/**
 * 验证必填字段
 * @param {Object} data 数据对象
 * @param {Array} requiredFields 必填字段列表
 * @returns {Array} 缺失的字段
 */
window.validateRequiredFields = function(data, requiredFields) {
    const missing = [];
    for (const field of requiredFields) {
        if (!data[field] || data[field] === '') {
            missing.push(field);
        }
    }
    return missing;
};

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间限制（毫秒）
 * @returns {Function} 节流后的函数
 */
window.throttle = function(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
window.debounce = function(func, delay) {
    let timeoutId;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(context, args), delay);
    };
};

/**
 * 获取任务类型图标
 * @param {string} type 任务类型
 * @returns {string} 图标HTML
 */
window.getTaskTypeIcon = function(type) {
    const iconMap = {
        'hand_card': '<i class="fas fa-hand-paper" style="color: #48bb78;"></i>',
        'audio': '<i class="fas fa-volume-up" style="color: #f6ad55;"></i>',
        'live': '<i class="fas fa-video" style="color: #4299e1;"></i>'
    };
    return iconMap[type] || '<i class="fas fa-cog"></i>';
};

/**
 * 获取任务类型颜色
 * @param {string} type 任务类型
 * @returns {string} 颜色值
 */
window.getTaskTypeColor = function(type) {
    const colorMap = {
        'hand_card': '#48bb78',
        'audio': '#f6ad55',
        'live': '#4299e1'
    };
    return colorMap[type] || '#667eea';
};

/**
 * 数组去重
 * @param {Array} array 原数组
 * @returns {Array} 去重后的数组
 */
window.uniqueArray = function(array) {
    return [...new Set(array)];
};

/**
 * 深拷贝对象
 * @param {*} obj 要拷贝的对象
 * @returns {*} 拷贝结果
 */
window.deepClone = function(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
        return obj.map(item => window.deepClone(item));
    }
    
    if (typeof obj === 'object') {
        const copy = {};
        Object.keys(obj).forEach(key => {
            copy[key] = window.deepClone(obj[key]);
        });
        return copy;
    }
    
    return obj;
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('前端通用工具模块加载完成');
});
