<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPU账号管理</title>

    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/jquery-3.5.0.min.js"></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/layer.3.5.1/layer.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        /* 顶部导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        
        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: 1px;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
        }
        
        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
        
        /* 主容器 */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        /* 统计卡片区域 */
        .stats-section {
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.2rem;
            color: #2d3748;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.12);
        }
        
        .stat-card:hover::before {
            transform: translateX(0);
        }
        
        .stat-card.total { --accent-color: #667eea; }
        .stat-card.active { --accent-color: #48bb78; }
        .stat-card.inactive { --accent-color: #718096; }
        .stat-card.expired { --accent-color: #f56565; }
        
        .stat-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }
        
        .stat-card.total .stat-icon { background: rgba(102, 126, 234, 0.1); color: #667eea; }
        .stat-card.active .stat-icon { background: rgba(72, 187, 120, 0.1); color: #48bb78; }
        .stat-card.inactive .stat-icon { background: rgba(113, 128, 150, 0.1); color: #718096; }
        .stat-card.expired .stat-icon { background: rgba(245, 101, 101, 0.1); color: #f56565; }
        
        .stat-number {
            font-size: 1.6rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            color: #718096;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        /* 操作区域 */
        .actions-section {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.06);
            margin-bottom: 2rem;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .actions-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .search-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex: 1;
            min-width: 200px;
        }

        .search-input {
            flex: 1;
            padding: 0.6rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f6ad55 0%, #d69e2e 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #edf2f7;
        }
        
        .btn-small {
            padding: 0.4rem 0.8rem;
            font-size: 0.75rem;
        }
        
        /* 账号表格区域 */
        .accounts-section {
            margin-bottom: 2rem;
        }
        
        .accounts-table-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.06);
            border: 1px solid rgba(0,0,0,0.05);
            overflow: hidden;
        }

        .accounts-table {
            width: 100%;
            border-collapse: collapse;
        }

        .accounts-table th {
            background: #f8fafc;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: #4a5568;
            border-bottom: 2px solid #e2e8f0;
            font-size: 0.85rem;
        }

        .accounts-table td {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            color: #2d3748;
            font-size: 0.9rem;
            vertical-align: middle;
        }

        .accounts-table tbody tr:hover {
            background: #f7fafc;
        }

        .account-status {
            display: inline-flex;
            align-items: center;
            padding: 0.3rem 0.8rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            white-space: nowrap;
        }
        
        .account-status.active {
            background: rgba(72, 187, 120, 0.1);
            color: #38a169;
            border: 1px solid rgba(72, 187, 120, 0.2);
        }
        
        .account-status.inactive {
            background: rgba(113, 128, 150, 0.1);
            color: #4a5568;
            border: 1px solid rgba(113, 128, 150, 0.2);
        }
        
        .account-status.expired {
            background: rgba(245, 101, 101, 0.1);
            color: #e53e3e;
            border: 1px solid rgba(245, 101, 101, 0.2);
        }

        .account-token {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.8rem;
            color: #718096;
        }

        .account-actions {
            display: flex;
            gap: 0.3rem;
            flex-wrap: wrap;
        }

        .no-data-row {
            text-align: center;
            padding: 3rem 1rem;
            color: #718096;
        }

        .no-data-row i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .actions-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-group {
                min-width: auto;
            }

            .accounts-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .account-card {
                padding: 1rem;
            }

            .account-times {
                flex-direction: column;
                gap: 0.5rem;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">GPU账号管理</div>
            <nav class="nav-links">
                <a href="live-plans.html" class="nav-link">
                    <i class="fas fa-video"></i> 直播计划
                </a>
                <a href="live-orders.html" class="nav-link">
                    <i class="fas fa-shopping-cart"></i> 直播订单
                </a>
                <a href="task-manager.html" class="nav-link">
                    <i class="fas fa-tasks"></i> 任务管理
                </a>
                <a href="gpu-accounts.html" class="nav-link active">
                    <i class="fas fa-server"></i> GPU账号
                </a>
                <a href="anchors.html" class="nav-link">
                    <i class="fas fa-microphone"></i> 主播管理
                </a>
            </nav>
        </div>
    </header>

    <div class="main-container">
        <!-- 统计卡片区域 -->
        <section class="stats-section">
            <h2 class="section-title">账号概览</h2>
            <div class="stats-grid" id="statsGrid">
                <!-- 默认占位 -->
                <div class="stat-card total">
                    <div class="stat-icon">🖥️</div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">总账号数</div>
                </div>
                <div class="stat-card active">
                    <div class="stat-icon">✅</div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">活跃账号</div>
                </div>
                <div class="stat-card inactive">
                    <div class="stat-icon">⏸️</div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">停用账号</div>
                </div>
                <div class="stat-card expired">
                    <div class="stat-icon">⏰</div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">过期账号</div>
                </div>
            </div>
        </section>

        <!-- 操作区域 -->
        <section class="actions-section">
            <div class="actions-row">
                <div class="search-group">
                    <input type="text" id="accountSearch" class="search-input" placeholder="搜索账号...">
                    <button type="button" onclick="loadAccounts()" class="btn btn-secondary">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
                <div>
                    <button type="button" onclick="showAddAccountModal()" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 添加账号
                    </button>
                    <button type="button" onclick="refreshAccounts()" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
        </section>

        <!-- 账号列表区域 -->
        <section class="accounts-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                <h3 class="section-title" style="margin-bottom: 0;">账号列表</h3>
            </div>
            <div class="accounts-table-container">
                <table class="accounts-table">
                    <thead>
                        <tr>
                            <th>账号</th>
                            <th>Token</th>
                            <th>API Key</th>
                            <th>镜像ID</th>
                            <th>钉钉Key</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>最后使用</th>
                            <th style="text-align: center;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="accountsList">
                        <!-- 账号数据将通过JS动态生成 -->
                    </tbody>
                </table>
            </div>
        </section>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
        layui.use(['layer', 'form'], function(){
            const layer = layui.layer;
            const form = layui.form;
            
            // 初始化
            $(document).ready(function() {
                init();
            });
            
            function init() {
                loadStats();
                loadAccounts();

                // 搜索框回车事件
                $('#accountSearch').on('keypress', function(e) {
                    if (e.which === 13) {
                        loadAccounts();
                    }
                });
            }
            
            // 加载统计信息
            async function loadStats() {
                try {
                    const response = await fetch('/api/gpu-accounts/stats', {
                        headers: getApiKeyHeader()
                    });
                    const result = await response.json();
                    
                    if (result.success) {
                        renderStats(result.stats);
                    }
                } catch (error) {
                    console.error('加载统计信息失败:', error);
                }
            }
            
            // 渲染统计信息
            function renderStats(stats) {
                $('#statsGrid').html(`
                    <div class="stat-card total">
                        <div class="stat-icon">🖥️</div>
                        <div class="stat-number">${stats.total || 0}</div>
                        <div class="stat-label">总账号数</div>
                    </div>
                    <div class="stat-card active">
                        <div class="stat-icon">✅</div>
                        <div class="stat-number">${stats.active || 0}</div>
                        <div class="stat-label">活跃账号</div>
                    </div>
                    <div class="stat-card inactive">
                        <div class="stat-icon">⏸️</div>
                        <div class="stat-number">${stats.inactive || 0}</div>
                        <div class="stat-label">停用账号</div>
                    </div>
                    <div class="stat-card expired">
                        <div class="stat-icon">⏰</div>
                        <div class="stat-number">${stats.expired || 0}</div>
                        <div class="stat-label">过期账号</div>
                    </div>
                `);
            }
            
            // 加载账号列表
            async function loadAccounts() {
                try {
                    const searchKeyword = $('#accountSearch').val();
                    const params = new URLSearchParams();
                    if (searchKeyword) {
                        params.append('search', searchKeyword);
                    }

                    const response = await fetch(`/api/gpu-accounts?${params}`, {
                        headers: getApiKeyHeader()
                    });
                    const result = await response.json();

                    if (result.success) {
                        renderAccounts(result.accounts);
                    } else {
                        layer.msg('加载账号列表失败: ' + result.message, {icon: 2});
                    }
                } catch (error) {
                    console.error('加载账号列表失败:', error);
                    layer.msg('加载账号列表失败', {icon: 2});
                }
            }

            // 渲染账号列表
            function renderAccounts(accounts) {
                if (accounts.length === 0) {
                    $('#accountsList').html(`
                        <tr>
                            <td colspan="10" class="no-data-row">
                                <i class="fas fa-server"></i>
                                <div>暂无GPU账号数据</div>
                            </td>
                        </tr>
                    `);
                    return;
                }

                $('#accountsList').html(accounts.map(account => `
                    <tr>
                        <td>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-user-circle" style="color: #667eea;"></i>
                                <strong>${account.account}</strong>
                            </div>
                        </td>
                        <td>
                            ${account.token ? `
                                <span class="account-token" title="${account.token}">${account.token.length > 10 ? account.token.substring(0, 10) + '...' : account.token}</span>
                            ` : '<span style="color: #cbd5e0;">未设置</span>'}
                        </td>
                        <td>
                            ${account.api_key ? `
                                <span class="account-token" title="${account.api_key}">${account.api_key.length > 10 ? account.api_key.substring(0, 10) + '...' : account.api_key}</span>
                            ` : '<span style="color: #cbd5e0;">未设置</span>'}
                        </td>
                        <td>
                            ${account.image_id ? `
                                <span title="${account.image_id}">${account.image_id.length > 10 ? account.image_id.substring(0, 10) + '...' : account.image_id}</span>
                            ` : '<span style="color: #cbd5e0;">未设置</span>'}
                        </td>
                        <td>
                            ${account.ding_key ? `
                                <span title="${account.ding_key}">${account.ding_key.length > 10 ? account.ding_key.substring(0, 10) + '...' : account.ding_key}</span>
                            ` : '<span style="color: #cbd5e0;">未设置</span>'}
                        </td>
                        <td>
                            <span class="account-status ${account.status}">${getStatusText(account.status)}</span>
                        </td>
                       
                        <td>
                            <span style="font-size: 0.85rem; color: #718096;">${formatDateTime(account.created_at)}</span>
                        </td>
                        <td>
                            <span style="font-size: 0.85rem; color: #718096;">${account.last_used_at ? formatDateTime(account.last_used_at) : '未使用'}</span>
                        </td>
                        <td style="text-align: center;">
                            <div class="account-actions">
                                <button onclick="editAccount(${account.id})" class="btn btn-primary btn-small" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                ${account.status === 'active' ? `
                                    <button onclick="toggleAccountStatus(${account.id}, 'inactive')" class="btn btn-warning btn-small" title="停用">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                ` : `
                                    <button onclick="toggleAccountStatus(${account.id}, 'active')" class="btn btn-success btn-small" title="启用">
                                        <i class="fas fa-play"></i>
                                    </button>
                                `}
                                <button onclick="deleteAccount(${account.id})" class="btn btn-danger btn-small" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join(''));
            }

            // 显示添加账号弹窗
            function showAddAccountModal() {
                layer.open({
                    type: 1,
                    title: '<i class="fas fa-plus-circle"></i> 添加GPU账号',
                    area: ['600px', '750px'],
                    skin: 'demo-class',
                    content: `
                        <div style="padding: 30px; background: #f8fafc;">
                            <form id="addAccountForm">
                                <div style="margin-bottom: 25px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                        <i class="fas fa-user"></i> 账号 <span style="color: #ef4444;">*</span>
                                    </label>
                                    <input type="text" name="account" required 
                                           placeholder="请输入GPU账号" 
                                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                           onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                </div>
                                <div style="margin-bottom: 25px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                        <i class="fas fa-lock"></i> 密码 <span style="color: #ef4444;">*</span>
                                    </label>
                                    <input type="password" name="password" required 
                                           placeholder="请输入账号密码" 
                                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                           onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                </div>
                                <div style="margin-bottom: 25px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                        <i class="fas fa-key"></i> Token
                                    </label>
                                    <input type="text" name="token"
                                           placeholder="请输入API Token（可选）"
                                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                           onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                </div>
                                <div style="margin-bottom: 25px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                        <i class="fas fa-code"></i> API Key
                                    </label>
                                    <input type="text" name="api_key"
                                           placeholder="请输入API Key（可选）"
                                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                           onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                </div>
                                <div style="margin-bottom: 25px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                        <i class="fas fa-image"></i> 镜像ID
                                    </label>
                                    <input type="text" name="image_id"
                                           placeholder="请输入镜像ID（可选）"
                                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                           onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                </div>
                                <div style="margin-bottom: 25px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                        <i class="fas fa-bell"></i> 钉钉Key
                                    </label>
                                    <input type="text" name="ding_key"
                                           placeholder="请输入钉钉Key（可选）"
                                           style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                           onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                           onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                </div>
                               
                                <div style="text-align: right; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                                    <button type="button" onclick="layer.closeAll()" 
                                            style="margin-right: 12px; padding: 10px 20px; border: 2px solid #d1d5db; background: #f9fafb; color: #374151; border-radius: 8px; cursor: pointer; font-weight: 500; transition: all 0.3s ease;"
                                            onmouseover="this.style.background='#f3f4f6';"
                                            onmouseout="this.style.background='#f9fafb';">
                                        <i class="fas fa-times"></i> 取消
                                    </button>
                                    <button type="submit" 
                                            style="padding: 10px 20px; border: none; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; cursor: pointer; font-weight: 500; transition: all 0.3s ease;"
                                            onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.3)';"
                                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                                        <i class="fas fa-save"></i> 保存账号
                                    </button>
                                </div>
                            </form>
                        </div>
                    `,
                    success: function() {
                        $('#addAccountForm').on('submit', function(e) {
                            e.preventDefault();
                            addAccount();
                        });
                    }
                });
            }

            // 添加账号
            async function addAccount() {
                const formData = new FormData(document.getElementById('addAccountForm'));
                const accountData = Object.fromEntries(formData.entries());

                try {
                    const response = await fetch('/api/gpu-accounts', {
                        method: 'POST',
                        headers: {
                            ...getApiKeyHeader(),
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(accountData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        layer.closeAll();
                        layer.msg('账号添加成功', {icon: 1});
                        loadStats();
                        loadAccounts();
                    } else {
                        layer.msg('添加失败: ' + result.message, {icon: 2});
                    }
                } catch (error) {
                    layer.msg('添加失败', {icon: 2});
                }
            }

            // 编辑账号
            async function editAccount(accountId) {
                try {
                    // 获取账号详情
                    const response = await fetch(`/api/gpu-accounts/${accountId}`, {
                        headers: getApiKeyHeader()
                    });
                    const result = await response.json();

                    if (!result.success) {
                        layer.msg('获取账号信息失败', {icon: 2});
                        return;
                    }

                    const account = result.account;

                    layer.open({
                        type: 1,
                        title: '<i class="fas fa-edit"></i> 编辑GPU账号',
                        area: ['600px', '750px'],
                        skin: 'demo-class',
                        content: `
                            <div style="padding: 30px; background: #f8fafc;">
                                <form id="editAccountForm">
                                    <input type="hidden" name="id" value="${account.id}">
                                    <div style="margin-bottom: 25px;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                            <i class="fas fa-user"></i> 账号 <span style="color: #ef4444;">*</span>
                                        </label>
                                        <input type="text" name="account" value="${account.account}" required 
                                               placeholder="请输入GPU账号" 
                                               style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                               onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                    </div>
                                    <div style="margin-bottom: 25px;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                            <i class="fas fa-lock"></i> 密码 <span style="color: #ef4444;">*</span>
                                        </label>
                                        <input type="password" name="password" value="${account.password}" required 
                                               placeholder="请输入账号密码" 
                                               style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                               onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                    </div>
                                    <div style="margin-bottom: 25px;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                            <i class="fas fa-key"></i> Token
                                        </label>
                                        <input type="text" name="token" value="${account.token || ''}"
                                               placeholder="请输入API Token（可选）"
                                               style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                               onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                    </div>
                                    <div style="margin-bottom: 25px;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                            <i class="fas fa-code"></i> API Key
                                        </label>
                                        <input type="text" name="api_key" value="${account.api_key || ''}"
                                               placeholder="请输入API Key（可选）"
                                               style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                               onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                    </div>
                                    <div style="margin-bottom: 25px;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                            <i class="fas fa-image"></i> 镜像ID
                                        </label>
                                        <input type="text" name="image_id" value="${account.image_id || ''}"
                                               placeholder="请输入镜像ID（可选）"
                                               style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                               onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                    </div>
                                    <div style="margin-bottom: 25px;">
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #374151; font-size: 14px;">
                                            <i class="fas fa-bell"></i> 钉钉Key
                                        </label>
                                        <input type="text" name="ding_key" value="${account.ding_key || ''}"
                                               placeholder="请输入钉钉Key（可选）"
                                               style="width: 100%; padding: 12px 16px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; transition: all 0.3s ease;"
                                               onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)';"
                                               onblur="this.style.borderColor='#e5e7eb'; this.style.boxShadow='none';">
                                    </div>
                                   
                                    <div style="text-align: right; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                                        <button type="button" onclick="layer.closeAll()" 
                                                style="margin-right: 12px; padding: 10px 20px; border: 2px solid #d1d5db; background: #f9fafb; color: #374151; border-radius: 8px; cursor: pointer; font-weight: 500; transition: all 0.3s ease;"
                                                onmouseover="this.style.background='#f3f4f6';"
                                                onmouseout="this.style.background='#f9fafb';">
                                            <i class="fas fa-times"></i> 取消
                                        </button>
                                        <button type="submit" 
                                                style="padding: 10px 20px; border: none; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; cursor: pointer; font-weight: 500; transition: all 0.3s ease;"
                                                onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.3)';"
                                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                                            <i class="fas fa-save"></i> 更新账号
                                        </button>
                                    </div>
                                </form>
                            </div>
                        `,
                        success: function() {
                            $('#editAccountForm').on('submit', function(e) {
                                e.preventDefault();
                                updateAccount();
                            });
                        }
                    });
                } catch (error) {
                    layer.msg('获取账号信息失败', {icon: 2});
                }
            }

            // 更新账号
            async function updateAccount() {
                const formData = new FormData(document.getElementById('editAccountForm'));
                const accountData = Object.fromEntries(formData.entries());
                const accountId = accountData.id;
                delete accountData.id;

                try {
                    const response = await fetch(`/api/gpu-accounts/${accountId}`, {
                        method: 'PUT',
                        headers: {
                            ...getApiKeyHeader(),
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(accountData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        layer.closeAll();
                        layer.msg('账号更新成功', {icon: 1});
                        loadStats();
                        loadAccounts();
                    } else {
                        layer.msg('更新失败: ' + result.message, {icon: 2});
                    }
                } catch (error) {
                    layer.msg('更新失败', {icon: 2});
                }
            }

            // 切换账号状态
            async function toggleAccountStatus(accountId, newStatus) {
                const statusText = newStatus === 'active' ? '启用' : '停用';
                layer.confirm(`确定要${statusText}这个账号吗？`, {icon: 3}, async function(index) {
                    try {
                        const response = await fetch(`/api/gpu-accounts/${accountId}/status`, {
                            method: 'PUT',
                            headers: {
                                ...getApiKeyHeader(),
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ status: newStatus })
                        });

                        const result = await response.json();

                        if (result.success) {
                            layer.msg(`账号已${statusText}`, {icon: 1});
                            loadStats();
                            loadAccounts();
                        } else {
                            layer.msg(`${statusText}失败: ` + result.message, {icon: 2});
                        }
                    } catch (error) {
                        layer.msg(`${statusText}失败`, {icon: 2});
                    }
                    layer.close(index);
                });
            }

            // 删除账号
            async function deleteAccount(accountId) {
                layer.confirm('确定要删除这个账号吗？此操作不可恢复！', {icon: 3}, async function(index) {
                    try {
                        const response = await fetch(`/api/gpu-accounts/${accountId}`, {
                            method: 'DELETE',
                            headers: getApiKeyHeader()
                        });

                        const result = await response.json();

                        if (result.success) {
                            layer.msg('账号已删除', {icon: 1});
                            loadStats();
                            loadAccounts();
                        } else {
                            layer.msg('删除失败: ' + result.message, {icon: 2});
                        }
                    } catch (error) {
                        layer.msg('删除失败', {icon: 2});
                    }
                    layer.close(index);
                });
            }

            // 刷新账号列表
            function refreshAccounts() {
                $('#accountSearch').val('');
                loadStats();
                loadAccounts();
            }

            // 将函数暴露到全局作用域
            window.loadAccounts = loadAccounts;
            window.showAddAccountModal = showAddAccountModal;
            window.editAccount = editAccount;
            window.toggleAccountStatus = toggleAccountStatus;
            window.deleteAccount = deleteAccount;
            window.refreshAccounts = refreshAccounts;
            
            // 工具函数
            function getStatusText(status) {
                const statusMap = {
                    'active': '活跃',
                    'inactive': '停用',
                    'expired': '过期'
                };
                return statusMap[status] || status;
            }
            
            function formatDateTime(dateStr) {
                if (!dateStr) return '';
                return new Date(dateStr).toLocaleString('zh-CN');
            }
            
            function getApiKeyHeader() {
                const apiKey = localStorage.getItem('apiKey') || '';
                return {
                    'X-API-Key': apiKey,
                    'Content-Type': 'application/json'
                };
            }
        });
    </script>
</body>
</html>