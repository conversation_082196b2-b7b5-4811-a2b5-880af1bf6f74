import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 违禁词过滤器
 * 复用前端的违禁词检测逻辑，从 prohibited_words.json 读取违禁词
 */
class ProhibitedWordsFilter {
    constructor() {
        this.prohibitedWords = null;
        this.lastLoadTime = 0;
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
    }

    /**
     * 加载违禁词列表（从 JSON 文件）
     */
    async loadProhibitedWords() {
        const now = Date.now();

        // 如果缓存有效，直接返回
        if (this.prohibitedWords && (now - this.lastLoadTime) < this.cacheTimeout) {
            return this.prohibitedWords;
        }

        try {
            const jsonPath = path.join(__dirname, 'utils', 'prohibited_words.json');

            // 检查文件是否存在
            try {
                await fs.access(jsonPath);
            } catch (accessError) {
                console.warn(`⚠️ 违禁词文件不存在: ${jsonPath}`);
                this.prohibitedWords = [];
                this.lastLoadTime = now;
                return this.prohibitedWords;
            }

            const jsonContent = await fs.readFile(jsonPath, 'utf-8');
            const parsedWords = JSON.parse(jsonContent);

            // 验证数据格式
            if (!Array.isArray(parsedWords)) {
                throw new Error('违禁词文件格式错误：应该是字符串数组');
            }

            // 过滤空值和非字符串
            this.prohibitedWords = parsedWords
                .filter(word => typeof word === 'string' && word.trim().length > 0)
                .map(word => word.trim());

            this.lastLoadTime = now;

            console.log(`📋 违禁词列表加载完成，共 ${this.prohibitedWords.length} 个词汇`);
            return this.prohibitedWords;
        } catch (error) {
            console.error('❌ 加载违禁词列表失败:', error);
            // 加载失败时返回空数组，不影响主流程
            this.prohibitedWords = [];
            this.lastLoadTime = now;
            return this.prohibitedWords;
        }
    }

    /**
     * 检测并清理违禁词
     * 完全复用前端的 detectAndCleanProhibitedWords 逻辑
     * @param {string} content - 要检测的内容
     * @param {Array} prohibitedWords - 违禁词列表（可选，不传则自动加载）
     * @returns {Promise<{cleanedContent: string, foundWords: string[]}>}
     */
    async detectAndCleanProhibitedWords(content, prohibitedWords = null) {
        if (!content || !content.trim()) {
            return {
                cleanedContent: content,
                foundWords: []
            };
        }

        // 如果没有传入违禁词列表，则加载
        if (!prohibitedWords) {
            prohibitedWords = await this.loadProhibitedWords();
            if (!prohibitedWords || prohibitedWords.length === 0) {
                return {
                    cleanedContent: content,
                    foundWords: []
                };
            }
        }

        let cleanedContent = content;
        const foundWords = [];

        // 按长度倒序排列违禁词，优先处理长词汇，避免短词汇影响长词汇的检测
        const sortedWords = [...prohibitedWords].sort((a, b) => b.length - a.length);

        // 检测并删除违禁词
        sortedWords.forEach(word => {
            if (!word || !word.trim()) return;

            const trimmedWord = word.trim();

            // 检查当前内容中是否包含该违禁词
            if (cleanedContent.includes(trimmedWord)) {
                // 记录发现的违禁词（避免重复）
                if (!foundWords.includes(trimmedWord)) {
                    foundWords.push(trimmedWord);
                }

                // 直接删除违禁词，不替换为任何字符，保持原有格式
                const escapedWord = trimmedWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const regex = new RegExp(escapedWord, 'g');
                cleanedContent = cleanedContent.replace(regex, '');
            }
        });

        return {
            cleanedContent: cleanedContent,
            foundWords: foundWords
        };
    }

    /**
     * 过滤手卡内容中的违禁词
     * @param {string} scriptContent - 手卡内容
     * @returns {Promise<{cleanedContent: string, foundWords: string[], hasProhibitedWords: boolean}>}
     */
    async filterHandCardContent(scriptContent) {
        const result = await this.detectAndCleanProhibitedWords(scriptContent);

        return {
            cleanedContent: result.cleanedContent,
            foundWords: result.foundWords,
            hasProhibitedWords: result.foundWords.length > 0
        };
    }

    /**
     * 清除缓存，强制重新加载违禁词列表
     */
    clearCache() {
        this.prohibitedWords = null;
        this.lastLoadTime = 0;
        console.log('🗑️ 违禁词缓存已清除');
    }
}

// 创建单例实例
const prohibitedWordsFilter = new ProhibitedWordsFilter();

export default prohibitedWordsFilter;
